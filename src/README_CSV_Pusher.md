# CSV数据推送器使用说明

## 概述

CSV数据推送器是一个高可用、解耦、扩展性强的数据推送工具，用于读取CSV文件中的数据并推送到指定的API接口。

## 文件结构

```
src/
├── csv_data_pusher.py          # 主推送器程序
├── config.py                   # 配置文件
├── 秒笔补充地区工种推送.py        # 推送接口和时间格式化工具
├── 新华妙笔-工种.csv            # 工种数据文件
├── 新华妙笔-城市数据.csv         # 城市数据文件
└── README_CSV_Pusher.md        # 使用说明（本文件）
```

## 核心功能

### 1. 配置管理
- 支持灵活的配置参数
- 测试模式和生产模式切换
- 批处理和延迟控制

### 2. 数据处理
- 自动读取大型CSV文件（支持低内存模式）
- 智能时间格式化（支持多种时间格式）
- 自动生成UUID（如果数据中缺失）
- 数据类型转换和验证

### 3. 推送机制
- 批量处理，避免服务器压力
- 重试机制，提高成功率
- 详细的日志记录
- 统计信息展示

### 4. 错误处理
- 完善的异常捕获
- 失败重试策略
- 详细的错误日志

## 配置说明

### config.py 配置项

```python
# CSV文件配置
CSV_FILES = ["新华妙笔-工种.csv", "新华妙笔-城市数据.csv"]

# 推送配置
BATCH_SIZE = 10                 # 每批处理数量
DELAY_SECONDS = 1               # 批次间延迟时间（秒）
FILE_DELAY_SECONDS = 2          # 文件间延迟时间（秒）

# 测试配置
TEST_MODE = True                # 测试模式，不实际推送
MAX_RECORDS_PER_FILE = 10       # 测试时每个文件最大处理记录数

# 推送接口配置
PUSH_API_URL = "http://192.168.0.133:7720/import/gather-clean"
PUSH_API_URL_PROD = "https://writing.botsmart.cn/api/import/gather-clean"

# 重试配置
MAX_RETRY_COUNT = 3             # 最大重试次数
RETRY_DELAY = 5                 # 重试延迟时间（秒）
```

## 使用方法

### 1. 测试模式运行

默认配置为测试模式，只会处理少量数据并模拟推送：

```bash
cd src
python csv_data_pusher.py
```

### 2. 生产模式运行

修改 `config.py` 中的配置：

```python
TEST_MODE = False               # 关闭测试模式
MAX_RECORDS_PER_FILE = None     # 处理全部数据
```

然后运行：

```bash
cd src
python csv_data_pusher.py
```

### 3. 自定义配置运行

```python
from csv_data_pusher import CSVDataPusher
from config import Config

# 自定义配置
Config.BATCH_SIZE = 5
Config.DELAY_SECONDS = 0.5
Config.TEST_MODE = False

# 创建推送器
pusher = CSVDataPusher()

# 处理指定文件
csv_files = ["your_custom_file.csv"]
pusher.process_all_csv_files(csv_files)
```

## 数据格式要求

CSV文件需要包含以下列（与推送接口对应）：

| 列名 | 类型 | 说明 | 必填 |
|------|------|------|------|
| uuid | string | 唯一标识符 | 否（自动生成）|
| title | string | 标题 | 是 |
| content | string | 内容 | 是 |
| formatContent | string | 格式化内容 | 否 |
| sourceUrl | string | 来源URL | 是 |
| publishTime | string | 发布时间 | 是 |
| source | string | 来源 | 是 |
| docType | int | 文档类型 | 否（默认1）|
| author | string | 作者 | 否 |
| compile | string | 编译信息 | 否 |
| database | string | 数据库 | 否 |
| classification | string | 分类 | 否 |
| label | string | 标签 | 否 |

## 时间格式支持

系统支持多种时间格式的自动转换：

- `2025-07-02 10:30:15` (标准格式)
- `2025-07-02` (日期格式)
- `2025/7/2 0:00` (斜杠格式)
- `2025/07/02` (斜杠日期格式)
- `2025年7月2日` (中文格式)
- `2025-07-02T10:30:15` (ISO格式)

## 日志说明

程序运行时会输出详细的日志信息：

```
INFO:__main__:开始处理 2 个CSV文件
INFO:__main__:处理第 1/2 个文件: 新华妙笔-工种.csv
INFO:__main__:开始读取CSV文件: 新华妙笔-工种.csv
INFO:__main__:成功读取CSV文件，共 745 条数据
INFO:__main__:限制处理记录数为: 10
INFO:__main__:开始处理文件: 新华妙笔-工种.csv, 共 10 条记录
INFO:__main__:处理批次: 1, 记录范围: 1-5
INFO:__main__:[测试模式] 准备推送数据: 标题示例
...
INFO:__main__:==================================================
INFO:__main__:数据推送完成！统计信息:
INFO:__main__:总处理记录数: 20
INFO:__main__:成功推送: 20
INFO:__main__:失败数量: 0
INFO:__main__:成功率: 100.00%
INFO:__main__:总耗时: 3.40 秒
INFO:__main__:==================================================
```

## 性能优化建议

### 1. 批处理大小调优
- 小批次（5-10条）：适合网络不稳定的环境
- 中批次（10-50条）：适合正常网络环境
- 大批次（50-100条）：适合高性能环境

### 2. 延迟时间调优
- 高延迟（2-5秒）：避免服务器压力，适合大数据量
- 中延迟（0.5-2秒）：平衡性能和稳定性
- 低延迟（0.1-0.5秒）：高性能处理，需确保服务器能力

### 3. 内存优化
- 程序使用 `low_memory=False` 读取CSV，适合大文件
- 分批处理避免内存溢出
- 处理完成后自动清理内存

## 错误处理

### 常见错误及解决方案

1. **文件不存在**
   ```
   ERROR:__main__:文件不存在: xxx.csv
   ```
   解决：检查文件路径和文件名

2. **时间格式化失败**
   ```
   ⚠️ 时间格式化失败: xxx, 错误: xxx
   ```
   解决：程序会自动使用当前时间，无需手动处理

3. **推送失败**
   ```
   ERROR:__main__:推送数据失败: xxx
   ```
   解决：程序会自动重试，检查网络连接和API状态

4. **CSV读取失败**
   ```
   ERROR:__main__:读取CSV文件失败: xxx
   ```
   解决：检查文件格式和编码（需要UTF-8）

## 扩展开发

### 添加新的数据源

1. 在 `config.py` 中添加新的CSV文件路径
2. 确保CSV格式符合要求
3. 运行程序即可

### 自定义推送接口

1. 修改 `秒笔补充地区工种推送.py` 中的 `push_web` 函数
2. 或在 `csv_data_pusher.py` 中重写 `push_single_data` 方法

### 添加数据验证

在 `prepare_data_for_push` 方法中添加自定义验证逻辑：

```python
def prepare_data_for_push(self, row):
    # 原有逻辑...
    
    # 添加自定义验证
    if not data['title'] or len(data['title']) < 10:
        logger.warning(f"标题过短，跳过: {data['title']}")
        return None
    
    return data
```

## 联系支持

如遇到问题或需要功能扩展，请联系开发团队。 