"""
@File    : main.py
@Software: PyCharm
@Modify Time      <AUTHOR>    @Desciption
------------      -------    --------    -----------
2022/2/23 10:52  xing       1.0          Octopus API
"""
import json
import time
import os
import traceback

import requests

from src.utils.Format import Format

from src.utils.BazhuayuUtils import Interface_Call_Credentials, this_task, Cloud_Collection_Related, Data_Collection_Related

def csv_read(path):
    with open(path, 'r', encoding='utf-8') as f:
        return f.read()

def push_web(uuid='', title='', content='', formatContent='', source='', classification='', label='', database='', sourceUrl='', author='', compile='', publishTime=''):
    body = [
        {
            "uuid": uuid,
            "title": title,
            "content": content,
            "formatContent": formatContent,
            "docType": 1,
            "source": source,
            "classification": classification,
            "label": label,
            "database": database,
            "sourceUrl": sourceUrl,
            "author": author,
            "compile": compile,
            "publishTime": publishTime
        }
    ]
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
    }
    try:
        re = requests.post(url="https://writing.botsmart.cn/api/import/gather-clean", json=body, headers=headers, timeout=160)
        a = re.text
        data = json.loads(a)
        print(data)
    except Exception as e:
        traceback.print_exc()
        print('提交失败!!!')

def service(datas):
    for data in datas:
        if data['title'] == '' or data['sourceUrl'] == '' or data['formatContent'] == '':
            continue
        format_html, download = Format.post_html_format(content_html=data['formatContent'], url=data['sourceUrl'], title=data['title'])
        if download is None or not '<html>' in format_html:
            print('转换错误!!!')
        elif download == 'success':
            push_web(uuid=data['uuid'],
                     title=data['title'],
                     content=data['content'],
                     formatContent=format_html,
                     source=data['source'],
                     classification=data['classification'],
                     label=data['label'],
                     database=data['database'],
                     sourceUrl=data['sourceUrl'],
                     author=data['author'],
                     compile=data['compile'],
                     publishTime=data['publishTime'])

    # Call the  function that Marks the data as exported,the taskId from this_task.get_search_task(),str


if __name__ == '__main__':
    # Instantiate class
    Interface_Call_Credentials = Interface_Call_Credentials()
    this_task = this_task()
    Cloud_Collection_Related = Cloud_Collection_Related()
    Data_Collection_Related = Data_Collection_Related()

    # Please enter an item in an account number password
    Interface_Call_Credentials.user_name = '***********'
    Interface_Call_Credentials.password = '1q2w3e4r'

    # Call the function that gets the new token of the account
    token_info = Interface_Call_Credentials.get_new_token()
    files_path = 'C:/Users/<USER>/Desktop/'
    # Call the function that refreshes the token interface
    # token_info = Interface_Call_Credentials.The_refresh_Token(token_info['refresh_token'])

    token_str = token_info['token_type'] + ' ' + token_info['access_token']

    # Every operation needs to obtain token_str

    # Call the function that get the task groups of the account
    # groups = this_task.get_task_group(token_str)
    # '2966111': 意识形态文章采集, '4133802': 公文采集_妙笔, '4200801': 学习园地_王, '4241745': 学习园地_于
    # '4380961': 公文采集_站外网公文库, '4493440': 山东省数据库采集, '4528271': 山东省数据库采集第二期, '高质量发展': '5015602'
    # groups = {'意识形态': '2966111', '妙笔公文': '4133802', '学习园地_王': '4200801', '学习园地_于': '4241745',
    #           '外网公文库': '4380961', '山东省人民政府_第一期': '4493440', '山东省人民政府_第二期': '4528271', '高质量发展': '5015602', '学习园地_新增': '5238808'}
    groups = {'学习园地_全自动采集入库': '5445467'}
    # groups = {'全国各地政府单位文件': '4867603'}
    # Call the function that get the task id of the account,the task_group_id from this_task.get_task_group(),str
    # tasks_info = {'taskId': '7203be38-f724-436f-a538-da9ae1106822', 'taskName': '地方政策_领导讲话_山东省省长周乃翔'}
    # data_list = Data_Collection_Related.Get_unexported_data(token=token_str, taskId=tasks_info['taskId'], size=1000)
    # service(datas=data_list['data'])
    # Data_Collection_Related.Marks_data_exported(token=token_str, taskId=tasks_info['taskId'])
    datas_len = 0
    for task_group_id in groups:
        tasks_infos = this_task.get_search_task(token=token_str, taskGroupId=groups[task_group_id])
        # datas = []

        for tasks_info in tasks_infos:
            size = '1000'  # e.g.
            # Call the  function that get task unexported data,the taskId from this_task.get_search_task(),str
            # if os.path.isfile(files_path + task_group_id + '/' + tasks_info['taskName'] + '.csv'):
            #     continue
            # size = '400'  # e.g.
            offset = '0'
            have_data = True
            datas = []
            while have_data:
                try:
                    # data_list, offset, have_data = Data_Collection_Related.get_task_all_data(token=token_str, taskId=tasks_info['taskId'], offset=offset, size=size)
                    data_list = Data_Collection_Related.Get_unexported_data(token=token_str, taskId=tasks_info['taskId'], size=size)
                    have_data = False
                    data = data_list['data']
                    if data is None:
                        break
                    for da in data:
                        datas.append(da)
                        datas_len = datas_len + 1
                except:
                    time.sleep(10)
            # for i in range(0, 10):
            #     try:
            #         data_list = Data_Collection_Related.Get_unexported_data(token=token_str, taskId=tasks_info['taskId'], size=size)
            #         break
            #     except:
            #         time.sleep(1)
            # datas = data_list['data']
            if datas is None or datas == []:
                continue
            print(tasks_info['taskName'])
            service(datas=datas)
            Data_Collection_Related.Marks_data_exported(token=token_str, taskId=tasks_info['taskId'])
    # tasks_info = {'taskId': '67b1510d-0218-3f57-c552-05abdee53f55', 'taskName': '二十届三中全会', 'creationUserId': 'bab061ba-edc5-4460-8a5b-43a4e1281216'}
    # data_list = Data_Collection_Related.Get_unexported_data(token=token_str, taskId=tasks_info['taskId'], size=1000)
    # service(files_path=files_path, task_group_id='', tasks_info=tasks_info, datas=data_list['data'])
    # Data_Collection_Related.Marks_data_exported(token=token_str, taskId=tasks_info['taskId'])

