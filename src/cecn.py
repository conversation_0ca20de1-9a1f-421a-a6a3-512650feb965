import json
import traceback
import logging
from urllib.parse import urljoin
from datetime import datetime, timedelta
import re

import requests

from utils.Format import Format
from utils.XPathUtils import XPathUtils
from utils.ArticleExtractor import extract_article

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def format_publish_time(time_str):
    """
    将publishTime格式化为yyyy-MM-dd HH:mm:ss格式
    
    Args:
        time_str: 原始时间字符串，可能的格式如：
                  - "2025-07-02"
                  - "2025-07-02 10:30:15"
                  - "2025/07/02"
                  - "2025年7月2日"
                  - "2025-07-02T10:30:15"
    
    Returns:
        str: 格式化后的时间字符串 "yyyy-MM-dd HH:mm:ss"
    """
    if not time_str or not isinstance(time_str, str):
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    time_str = time_str.strip()
    
    try:
        # 如果已经是完整格式，直接返回
        if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', time_str):
            return time_str
        
        # 处理 ISO 格式 (2025-07-02T10:30:15)
        if 'T' in time_str:
            time_str = time_str.split('T')[0] + ' ' + time_str.split('T')[1].split('+')[0].split('Z')[0]
            if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', time_str):
                return time_str[:19]  # 取前19个字符
        
        # 处理日期格式 (2025-07-02)
        if re.match(r'^\d{4}-\d{2}-\d{2}$', time_str):
            return time_str + " 00:00:00"
        
        # 处理斜杠格式 (2025/07/02)
        if re.match(r'^\d{4}/\d{2}/\d{2}$', time_str):
            return time_str.replace('/', '-') + " 00:00:00"
        
        # 处理中文格式 (2025年7月2日)
        if '年' in time_str and '月' in time_str and '日' in time_str:
            # 提取年月日
            year_match = re.search(r'(\d{4})年', time_str)
            month_match = re.search(r'(\d{1,2})月', time_str)
            day_match = re.search(r'(\d{1,2})日', time_str)
            
            if year_match and month_match and day_match:
                year = year_match.group(1)
                month = month_match.group(1).zfill(2)
                day = day_match.group(1).zfill(2)
                return f"{year}-{month}-{day} 00:00:00"
        
        # 如果都不匹配，尝试用datetime解析
        dt = datetime.strptime(time_str, "%Y-%m-%d")
        return dt.strftime("%Y-%m-%d %H:%M:%S")
        
    except Exception as e:
        print(f"⚠️ 时间格式化失败: {time_str}, 错误: {str(e)}")
        # 返回当前时间作为默认值
        return ''


def push_web(data):
    body = [
        {
            "uuid": data['uuid'],
            "title": data['title'],
            "content": data['content'],
            "formatContent": data['formatContent'],
            "docType": 1,
            "source": data['source'],
            "classification": data['classification'],
            "label": data['label'],
            "database": data['database'],
            "sourceUrl": data['sourceUrl'],
            "author": data['author'],
            "compile": data['compile'],
            "publishTime": data['publishTime']
        }
    ]
    print(body)
    # headers = {
    #     'Content-Type': 'application/json',
    #     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
    # }
    # try:
    #     # re = requests.post(url="http://192.168.0.133:7720/import/gather-clean", json=body, headers=headers, timeout=160)
    #     re = requests.post(url="https://writing.botsmart.cn/api/import/gather-clean", json=body, headers=headers, timeout=160)
    #     a = re.text
    #     data = json.loads(a)
    #     print(data)
    # except Exception as e:
    #     traceback.print_exc()
    #     print('提交失败!!!')


def service(datas):
    for data in datas:
        if data['title'] == '' or data['sourceUrl'] == '' or data['formatContent'] == '':
            continue
        format_html, download = Format.post_html_format(content_html=data['formatContent'], url=data['sourceUrl'], title=data['title'])
        if download is None or not '<html>' in format_html:
            print('转换错误!!!')
        elif download == 'success':
            print('download')
            # push_web(data)

def generate_daily_urls(target_date=None):
    """
    生成指定日期的经济日报页面URL列表

    Args:
        target_date: 目标日期，格式为 datetime 对象，默认为今天

    Returns:
        list: URL列表，从 node_01.html 到 node_12.html
    """
    if target_date is None:
        target_date = datetime.now()

    # 格式化日期为 YYYYMM/DD 格式
    date_str = target_date.strftime("%Y%m/%d")

    urls = []
    base_url = f"http://paper.ce.cn/pc/layout/{date_str}"

    # 生成从 node_01.html 到 node_12.html 的URL
    for i in range(1, 13):
        node_num = f"{i:02d}"  # 格式化为两位数，如 01, 02, ..., 12
        url = f"{base_url}/node_{node_num}.html"
        urls.append(url)

    print(f"📅 生成日期 {target_date.strftime('%Y-%m-%d')} 的URL列表:")
    for i, url in enumerate(urls, 1):
        print(f"   {i:2d}. {url}")

    return urls

def get_default_xpath_config():
    """
    获取默认的XPath配置

    Returns:
        dict: 包含所有XPath表达式的字典
    """
    return {
        'uuid': '',  # 留空
        'classification': '经济日报',  # 分类
        'label': '央媒',  # 标签
        'database': '最新新闻',  # 数据库
        'author': '',  # 作者
        'compile': '',  # 留空

        'base_url': 'http://paper.ce.cn/pc/layout',
        'main_xpath': '',
        'sub_xpath': '',
        'focuslist_xpath': '//ul[@class="newsList"]/li/a',   # 右侧列表新闻
        'detail_title_xpath': '//h2[@id="Title"]/text()',
        'detail_source_xpath': '',
        'detail_time_xpath': '//span[@id="paperdate"]/text()',
        'detail_content_xpath': '//div[@id="ozoom"]'
    }

def get_news_detail(detail_url, detail_title_xpath, detail_source_xpath, detail_time_xpath, detail_content_xpath, config=None, use_xpath_with_images=True):
    """
    获取新闻详情页面的数据
    
    Args:
        detail_url: 详情页面URL
        detail_title_xpath: 详情页标题XPath
        detail_source_xpath: 详情页来源XPath
        detail_time_xpath: 详情页时间XPath
        detail_content_xpath: 详情页内容XPath
        config: 配置字典，包含默认值
        use_xpath_with_images: 是否使用XPath方法并下载图片到本地
        
    Returns:
        dict: 包含标题、来源、时间、正文的字典
    """
    print(f"正在获取详情页面: {detail_url}")
    
    # 获取详情页面HTML
    html_content, status_code = XPathUtils.get_html_content(detail_url)
    if not html_content:
        print(f"获取详情页面失败: {detail_url}")
        return None
    
    # 检查是否为有效的新闻页面（过滤视频和专题页面）
    if not is_valid_news_page(html_content, detail_url):
        print(f"❌ 页面被过滤，跳过详情页: {detail_url}")
        return None
    
    # 解析HTML
    tree = XPathUtils.parse_html(html_content)
    if tree is None:
        print(f"解析详情页面HTML失败: {detail_url}")
        return None
    
    # 提取详情页面数据
    detail_title = XPathUtils.safe_xpath_extract(tree, detail_title_xpath)
    detail_source = XPathUtils.safe_xpath_extract(tree, detail_source_xpath)
    detail_publish_time = XPathUtils.safe_xpath_extract(tree, detail_time_xpath)
    print(detail_publish_time)
    # 提取各部分
    # year_xpath = detail_time_xpath.split('|')[0]
    # datestr_xpath = detail_time_xpath.split('|')[1]
    # times_xpath = detail_time_xpath.split('|')[2]

    # year = XPathUtils.safe_xpath_extract(tree, year_xpath)
    # month_day = XPathUtils.safe_xpath_extract(tree, datestr_xpath).replace('/', '-')
    # time = XPathUtils.safe_xpath_extract(tree, times_xpath)

    # 拼接成目标格式
    # detail_publish_time = f"{year}-{month_day} {time}"


    # 清理和处理数据
    if detail_source:
        detail_source = detail_source.replace('来源：', '').strip()
    
    # 使用ArticleExtractor提取content、formatContent和author
    if use_xpath_with_images:
        print("🔄 使用ArticleExtractor XPath方法提取文章内容并下载图片...")
        article_result = extract_article(detail_url, detail_title, detail_content_xpath)  # 传递content_xpath，使用XPath方法
    else:
        print("🔄 使用ArticleExtractor传统方法提取文章内容...")
        article_result = extract_article(detail_url, detail_title)  # 不传递content_xpath，使用传统方法
    
    # 从ArticleExtractor结果中获取数据
    extracted_content = article_result.get('text', '')  # 纯文本内容
    extracted_html = article_result.get('html', '')     # HTML格式内容
    extracted_author = article_result.get('author', '') # 作者
    
    if article_result.get('error'):
        print(f"⚠️ ArticleExtractor提取过程中出现警告: {article_result['error']}")
        # 如果ArticleExtractor失败，回退到原始方法
        extracted_content = XPathUtils.safe_xpath_extract(tree, detail_content_xpath)
        extracted_html = XPathUtils.safe_xpath_extract_raw(tree, detail_content_xpath)

    if not extracted_content or not extracted_html:
        print(f"ArticleExtractor提取后获取详情页面失败: {detail_url}")
        return None

    print(f"✅ 提取完成 - 文本长度: {len(extracted_content)}字符, HTML长度: {len(extracted_html)}字符, 作者: {extracted_author}")
    
    # 使用配置中的默认值，如果没有配置则使用空字符串
    default_config = config if config else {}
    
    # 格式化发布时间
    formatted_publish_time = format_publish_time(detail_publish_time)
    
    result = {
        'uuid': default_config.get('uuid', ''),
        'title': detail_title,
        'content': extracted_content,      # 使用ArticleExtractor的纯文本内容
        'formatContent': extracted_html,   # 使用ArticleExtractor的HTML内容
        'source': detail_source,
        'classification': default_config.get('classification', ''),
        'label': default_config['label'],
        'database': default_config.get('database', ''),
        'sourceUrl': detail_url,
        'author': extracted_author or default_config.get('author', ''),  # 优先使用提取的作者
        'compile': default_config.get('compile', ''),
        'publishTime': formatted_publish_time  # 使用格式化后的时间
    }
    
    return result

def fetch_main_news_from_tree(tree, base_url, main_xpath, detail_title_xpath, detail_source_xpath, detail_time_xpath, detail_content_xpath, config=None, use_xpath_with_images=True):
    """
    从预解析的HTML树中抓取主新闻数据
    
    Args:
        tree: 预解析的HTML树
        base_url: 经济日报首页URL
        main_xpath: 主新闻XPath表达式
        detail_title_xpath: 详情页标题XPath
        detail_source_xpath: 详情页来源XPath  
        detail_time_xpath: 详情页时间XPath
        detail_content_xpath: 详情页内容XPath
        config: 配置字典，包含默认值
        
    Returns:
        dict: 主新闻数据
    """
    
    # 提取主新闻链接和标题
    main_elements = tree.xpath(main_xpath)
    
    if not main_elements:
        print("未找到主新闻元素")
        return None
    
    main_element = main_elements[0]
    main_title = XPathUtils.safe_xpath_extract(tree, main_xpath + '/text()')
    main_url = main_element.get('href', '')
    
    # 处理相对URL
    if main_url and not main_url.startswith('http'):
        main_url = urljoin(base_url, main_url)
    
    print(f"主新闻标题: {main_title}")
    print(f"主新闻链接: {main_url}")
    
    # 获取主新闻详情
    if main_url:
        detail_data = get_news_detail(main_url, detail_title_xpath, detail_source_xpath, detail_time_xpath, detail_content_xpath, config, use_xpath_with_images)
        if detail_data:
            return detail_data
        else:
            print("获取主新闻详情失败")
            return None
    else:
        print("主新闻链接为空")
        return None

def fetch_sub_news_from_tree(tree, base_url, sub_xpath, detail_title_xpath, detail_source_xpath, detail_time_xpath, detail_content_xpath, config=None, use_xpath_with_images=True):
    """
    从预解析的HTML树中抓取子新闻数据
    
    Args:
        tree: 预解析的HTML树
        base_url: 经济日报首页URL
        sub_xpath: 子新闻XPath表达式
        detail_title_xpath: 详情页标题XPath
        detail_source_xpath: 详情页来源XPath  
        detail_time_xpath: 详情页时间XPath
        detail_content_xpath: 详情页内容XPath
        config: 配置字典，包含默认值
        
    Returns:
        list: 子新闻数据列表
    """
    
    # 提取子新闻链接和标题
    if sub_xpath is None or sub_xpath == '':
        print("未配置子新闻xpath")
        return []

    sub_elements = tree.xpath(sub_xpath)
    
    if not sub_elements:
        print("未找到子新闻元素")
        return []
    
    print(f"找到 {len(sub_elements)} 个子新闻")
    
    sub_news_list = []
    
    for i, sub_element in enumerate(sub_elements, 1):
        try:
            sub_title = sub_element.text_content().strip() if sub_element.text_content() else ""
            sub_url = sub_element.get('href', '')
            
            # 处理相对URL
            if sub_url and not sub_url.startswith('http'):
                sub_url = urljoin(base_url, sub_url)
            
            print(f"\n📰 子新闻 {i}: {sub_title}")
            print(f"🔗 链接: {sub_url}")
            
            # 获取子新闻详情
            if sub_url:
                detail_data = get_news_detail(sub_url, detail_title_xpath, detail_source_xpath, detail_time_xpath, detail_content_xpath, config, use_xpath_with_images)
                if detail_data:
                    sub_news_list.append(detail_data)
                    print(f"✅ 子新闻 {i} 提取成功")
                else:
                    print(f"❌ 子新闻 {i} 详情获取失败")
            else:
                print(f"❌ 子新闻 {i} 链接为空")
                
        except Exception as e:
            print(f"❌ 处理子新闻 {i} 时出错: {str(e)}")
            continue
    
    print(f"\n📊 总共成功提取了 {len(sub_news_list)} 个子新闻")
    return sub_news_list

def fetch_focuslist_news_from_tree(tree, base_url, focuslist_xpath, detail_title_xpath, detail_source_xpath, detail_time_xpath, detail_content_xpath, config=None, max_count=12, use_xpath_with_images=True):
    """
    从预解析的HTML树中抓取右侧列表新闻数据
    
    Args:
        tree: 预解析的HTML树
        base_url: 经济日报首页URL
        focuslist_xpath: 右侧列表新闻XPath表达式
        detail_title_xpath: 详情页标题XPath
        detail_source_xpath: 详情页来源XPath  
        detail_time_xpath: 详情页时间XPath
        detail_content_xpath: 详情页内容XPath
        config: 配置字典，包含默认值
        max_count: 最大抓取数量，默认12条
        
    Returns:
        list: 右侧列表新闻数据列表
    """
    
    # 提取右侧列表新闻链接
    # 使用传入的focuslist_xpath参数，但需要修改为提取链接元素
    # 将原来的xpath从取span改为取a标签
    # if focuslist_xpath.endswith('//li/span'):
    #     # 如果是取span的xpath，改为取a标签
    #     link_xpath = focuslist_xpath.replace('//li/span', '//li//a')
    # else:
    #     # 否则在原xpath基础上添加//a
    #     link_xpath = focuslist_xpath + '//a'
    
    focuslist_elements = tree.xpath(focuslist_xpath)
    
    if not focuslist_elements:
        print("未找到右侧列表新闻元素")
        return []
    
    print(f"找到 {len(focuslist_elements)} 个右侧列表新闻候选")
    
    focuslist_news_list = []
    processed_count = 0
    valid_count = 0
    
    for i, focus_element in enumerate(focuslist_elements, 1):
        if valid_count >= max_count:
            print(f"✅ 已达到最大抓取数量限制: {max_count}")
            break
            
        try:
            focus_title = focus_element.text_content().strip() if focus_element.text_content() else ""
            focus_url = focus_element.get('href', '')
            
            # 处理相对URL
            if focus_url and not focus_url.startswith('http'):
                focus_url = urljoin(base_url, focus_url)
            
            processed_count += 1
            print(f"\n📰 右侧新闻 {processed_count}: {focus_title}")
            print(f"🔗 链接: {focus_url}")
            
            if not focus_url:
                print(f"❌ 链接为空，跳过")
                continue
            
            # 获取详情页HTML进行预检查
            print("🔍 正在检查页面类型...")
            detail_html_content, detail_status_code = XPathUtils.get_html_content(focus_url)
            
            if not detail_html_content:
                print(f"❌ 无法获取详情页面，跳过")
                continue
            
            # 进行页面有效性检查
            if not is_valid_news_page(detail_html_content, focus_url):
                print(f"❌ 页面被过滤，跳过")
                continue
            
            # 获取右侧列表新闻详情
            print("📄 开始提取新闻详情...")
            detail_data = get_news_detail(focus_url, detail_title_xpath, detail_source_xpath, detail_time_xpath, detail_content_xpath, config, use_xpath_with_images)
            if detail_data:
                focuslist_news_list.append(detail_data)
                valid_count += 1
                print(f"✅ 右侧新闻 {processed_count} 提取成功 (有效新闻: {valid_count})")
            else:
                print(f"❌ 右侧新闻 {processed_count} 详情获取失败")
                
        except Exception as e:
            print(f"❌ 处理右侧新闻 {processed_count} 时出错: {str(e)}")
            continue
    
    print(f"\n📊 右侧列表新闻处理完成:")
    print(f"   总候选数: {len(focuslist_elements)}")
    print(f"   已处理数: {processed_count}")
    print(f"   有效新闻: {len(focuslist_news_list)}")
    return focuslist_news_list

def is_valid_news_page(html_content, detail_url):
    """
    检查是否为有效的新闻详情页面
    过滤掉视频页面和专题页面
    
    Args:
        html_content: 页面HTML内容
        detail_url: 详情页URL
        
    Returns:
        bool: True表示是有效新闻页面，False表示需要过滤
    """
    if not html_content:
        return False

    # URL过滤 - 排除视频和专题类URL
    url_filters = [
        '/video/',      # 视频页面
        # '/special/',    # 专题页面
        '/topic/',      # 话题页面
        '/live/',       # 直播页面
        '/gallery/',    # 图集页面
        '/photo/',      # 图片页面
        '/zt/',         # 专题缩写
        # '.shtml',       # 某些特殊页面格式
    ]
    
    for filter_pattern in url_filters:
        if filter_pattern in detail_url.lower():
            print(f"❌ URL过滤: {filter_pattern} 在 {detail_url}")
            return False
    
    # HTML内容过滤 - 检查是否包含视频相关元素 (增强视频检测)
    video_indicators = [
        'class="video',        # 视频播放器类名
        'id="video',          # 视频播放器ID
        'data-video',         # 视频数据属性
        '"videoSrc"',         # 视频源属性
        'videojs',            # Video.js播放器
        '<video',             # video标签
        'embed-responsive',   # 响应式嵌入（通常用于视频）
        'jwplayer',           # JW Player
        'flowplayer',         # Flow Player
        '.mp4',               # MP4视频文件
        '.m3u8',              # HLS视频流
        '.flv',               # FLV视频文件
        'data-src="http',     # 视频数据源
        'video-player',       # 视频播放器
        'player-container',   # 播放器容器
        'video-wrap',         # 视频包装器
        # 暂时注释iframe检查，因为很多正常新闻页面也包含广告iframe
        # 'iframe',             # 可能嵌入视频的iframe
    ]
    
    for indicator in video_indicators:
        if indicator.lower() in html_content.lower():
            print(f"❌ 内容过滤: 检测到视频内容标识 '{indicator}'")
            return False
    
    # 专题页面过滤 - 检查特殊的专题页面标识
    special_indicators = [
        # 'class="special',     # 专题页面类名
        # 'id="special',        # 专题页面ID
        'data-special',       # 专题数据属性
        '"topicId"',          # 话题ID
        'class="topic',       # 话题页面
        'photo-gallery',      # 图集页面
        'slide-show',         # 幻灯片展示
    ]
    
    for indicator in special_indicators:
        if indicator.lower() in html_content.lower():
            print(f"❌ 内容过滤: 检测到专题内容标识 '{indicator}'")
            return False
    
    # 检查是否有正常的新闻内容结构
    normal_news_indicators = [
        'class="head-line',    # 经济日报新闻标题结构
        'class="header-cont',  # 经济日报新闻头部结构
        'id="detail"',         # 经济日报新闻内容区域
        'publishdate',         # 发布日期元信息
        'class="article',      # 新闻标题结构
        'class="TRS_UEDITOR',    # 新闻标题结构
    ]
    
    has_news_structure = False
    for indicator in normal_news_indicators:
        if indicator.lower() in html_content.lower():
            has_news_structure = True
            break
    
    if not has_news_structure:
        print(f"❌ 结构过滤: 页面缺少正常新闻结构")
        return False
    
    print(f"✅ 页面验证通过: {detail_url}")
    return True

def craw_cecn(target_date=None):
    """
    抓取经济日报新闻

    Args:
        target_date: 目标日期，格式为 datetime 对象，默认为今天
    """
    print("开始抓取经济日报新闻...")

    # 获取XPath配置
    config = get_default_xpath_config()

    # 生成当日的所有页面URL
    daily_urls = generate_daily_urls(target_date)

    all_news_list = []  # 存储所有采集到的新闻

    # 循环处理每个页面
    for page_num, page_url in enumerate(daily_urls, 1):
        print(f"\n{'='*80}")
        print(f"🔄 正在处理第 {page_num} 个页面: {page_url}")
        print(f"{'='*80}")

        # 获取页面HTML
        html_content, status_code = XPathUtils.get_html_content(page_url)
        if not html_content:
            print(f"❌ 获取页面失败: {page_url}")
            continue

        print(f"✅ 成功获取页面内容，状态码: {status_code}")

        # 解析HTML
        tree = XPathUtils.parse_html(html_content)
        if tree is None:
            print(f"❌ 解析页面HTML失败: {page_url}")
            continue

        print("✅ HTML解析完成，开始提取新闻列表...")

        # 根据focuslist_xpath抓取当前页面的新闻列表
        page_news_list = fetch_focuslist_news_from_tree(
            tree,  # 传递预解析的树
            config['base_url'],
            config['focuslist_xpath'],
            config['detail_title_xpath'],
            config['detail_source_xpath'],
            config['detail_time_xpath'],
            config['detail_content_xpath'],
            config,  # 传递完整配置
            max_count=50,  # 每页最多50条，根据实际情况调整
            use_xpath_with_images=True,  # 启用XPath方法和图片下载
        )

        if page_news_list:
            print(f"\n🎉 第 {page_num} 页成功抓取到 {len(page_news_list)} 条新闻")
            all_news_list.extend(page_news_list)

            # 推送当前页面的新闻
            for i, news in enumerate(page_news_list, 1):
                print(f"\n📰 第{page_num}页-新闻{i}:")
                print("-" * 40)
                print(f"title: {news['title']}")
                print(f"sourceUrl: {news['sourceUrl']}")
                print(f"publishTime: {news['publishTime']}")

                push_web(news)
        else:
            print(f"❌ 第 {page_num} 页未能获取到新闻数据")

    # 输出总结信息
    print(f"\n{'='*80}")
    print(f"📊 经济日报采集总结")
    print(f"{'='*80}")
    print(f"📅 采集日期: {target_date.strftime('%Y-%m-%d') if target_date else datetime.now().strftime('%Y-%m-%d')}")
    print(f"📄 处理页面数: {len(daily_urls)}")
    print(f"📰 总采集新闻数: {len(all_news_list)}")
    print(f"{'='*80}")

    return all_news_list

def testcecn():
    print('testcecn')

if __name__ == '__main__':
    # 默认采集今天的新闻
    craw_cecn()

    # 如果需要采集指定日期的新闻，可以这样调用：
    # target_date = datetime(2025, 9, 23)  # 2025年9月23日
    # craw_cecn(target_date)