"""
CSV数据推送配置文件
"""

import os

class Config:
    """配置类"""
    
    # CSV文件配置
    CSV_FILES = [
        # "新华妙笔-工种.csv",
        # "新华妙笔-城市数据.csv"
        "地市_success_20250711_153109的副本.csv",
        # "工种_success_20250711_153106.csv"
        # "工种_success_20250714_103721.csv",
        # "县委办_最新动态_detail.csv",
        # "团委_全团要讯_detail.csv",
        # "社会事务管理部门_政策文件_detail.csv",

    ]
    
    # 推送配置
    BATCH_SIZE = 1  # 每批处理数量
    DELAY_SECONDS = 1  # 批次间延迟时间（秒）
    FILE_DELAY_SECONDS = 2  # 文件间延迟时间（秒）
    
    # 测试配置
    TEST_MODE = False  # 测试模式，不实际推送
    MAX_RECORDS_PER_FILE = 1  # 测试时每个文件最大处理记录数，None表示处理全部
    
    # 推送接口配置
    # PUSH_API_URL = "http://192.168.0.133:7720/import/gather-clean"
    # PUSH_API_URL_PROD = "https://writing.botsmart.cn/api/import/gather-clean"
    
    # 请求配置
    REQUEST_TIMEOUT = 160  # 请求超时时间（秒）
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
    
    # 重试配置
    MAX_RETRY_COUNT = 3  # 最大重试次数
    RETRY_DELAY = 3  # 重试延迟时间（秒）
    
    @classmethod
    def get_push_url(cls, use_prod=False):
        """获取推送URL"""
        return cls.PUSH_API_URL_PROD if use_prod else cls.PUSH_API_URL
    
    @classmethod
    def get_csv_files(cls, base_path=""):
        """获取CSV文件路径列表"""
        if base_path:
            return [os.path.join(base_path, f) for f in cls.CSV_FILES]
        return cls.CSV_FILES 