# ArticleExtractor 配置文件
# 图片上传和存储配置

# MinIO 对象存储配置
minio:
  enabled: true                                    # 是否启用MinIO上传（默认开启）
  endpoint: "http://minio.miyun.botsmart.cn"      # MinIO服务地址
  port: 9000                                      # MinIO端口
  access_key: "YuN2vUABOkg6uMl0"                 # 访问密钥
  secret_key: "GUMqhs0lHBwHrXHt84cxyj2kME6HeYyX" # 秘密密钥
  bucket_name: "botsmart"                         # 存储桶名称
  folder_path: "temp_col_file/"                  # 图片存储文件夹 col_result_img/ temp_col_file/
  show_url: "http://minio.miyun.botsmart.cn"      # 公开访问地址
  show_https_url: "https://minio-miyun.botsmart.cn:19443"  # HTTPS访问地址
  timeout: 30                                     # 上传超时时间(秒)
  max_retries: 3                                  # 上传失败重试次数

# MySQL 数据库配置
database:
  enabled: true                                   # 是否启用数据库存储
  host: "dev-hw.botsmart.cn"                     # 数据库主机
  port: 33006                                     # 数据库端口
  username: "root"                                # 用户名
  password: "Botsmart@2"                          # 密码
  database: "bot_collection_data"                 # 数据库名
  charset: "utf8mb4"                              # 字符集
  
  # 连接池配置
  pool_size: 20                                   # 最大连接数
  max_overflow: 10                                # 超出连接池大小的连接数
  pool_timeout: 30                                # 获取连接超时时间
  pool_recycle: 3600                              # 连接回收时间(秒)
  echo: false                                     # 是否打印SQL语句

# 图片处理配置
image:
  local_dir: "src/news_images"                    # 本地图片保存目录
  max_size_mb: 5                                  # 最大图片大小(MB)
  max_width: 640                                  # 图片最大宽度(像素)
  allowed_formats: ["jpg", "jpeg", "png", "gif", "webp"]  # 允许的图片格式
  quality: 85                                     # 图片压缩质量(1-100)

# 日志配置
logging:
  level: "INFO"                                   # 日志级别
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/article_extractor.log"              # 日志文件路径

# 网络请求配置
network:
  timeout: [10, 30]                               # 请求超时时间 [连接超时, 读取超时]
  max_retries: 3                                  # 最大重试次数
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"

# 缓存配置
cache:
  enabled: true                                   # 是否启用缓存
  max_size: 50                                    # 最大缓存条目数
  ttl: 3600                                       # 缓存过期时间(秒) 