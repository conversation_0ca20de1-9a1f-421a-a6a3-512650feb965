import pandas as pd
import re
import os
from urllib.parse import urljoin, urlparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CSVCleaner:
    """CSV文件清洗器，专门处理附件链接"""
    
    def __init__(self):
        self.attachment_extensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx']
        self.processed_count = 0
        self.modified_count = 0
        
    def extract_base_url(self, source_url):
        """从sourceUrl提取基础URL（到最后一个/为止）"""
        try:
            if not source_url or pd.isna(source_url):
                return ''
            
            # 确保URL以http开头
            if not source_url.startswith(('http://', 'https://')):
                source_url = 'https://' + source_url
            
            # 解析URL并构建基础路径
            parsed = urlparse(source_url)
            base_path = parsed.path.rsplit('/', 1)[0] if '/' in parsed.path else ''
            base_url = f"{parsed.scheme}://{parsed.netloc}{base_path}/"
            
            return base_url
            
        except Exception as e:
            logger.warning(f"提取基础URL失败: {source_url}, 错误: {e}")
            return ''
    
    def find_attachment_links(self, content):
        """查找内容中的附件链接"""
        if not content or pd.isna(content):
            return []
        
        content = str(content)
        
        # 查找href链接中包含附件扩展名的
        href_pattern = r'href=["\']([^"\']*\.(?:pdf|doc|docx|xls|xlsx)[^"\']*)["\']'
        links = re.findall(href_pattern, content, re.IGNORECASE)
        
        return links
    
    def remove_duplicate_links(self, content, links):
        """删除重复的附件链接，只保留第一个"""
        if not links:
            return content
        
        # 统计每个链接的出现次数
        link_counts = {}
        for link in links:
            link_counts[link] = link_counts.get(link, 0) + 1
        
        # 对于出现多次的链接，删除重复的
        modified_content = content
        
        for link, count in link_counts.items():
            if count > 1:
                # 构建匹配模式
                escaped_link = re.escape(link)
                pattern = r'<a[^>]*href=["\']' + escaped_link + r'["\'][^>]*>.*?</a>'
                
                # 找到所有匹配的链接
                matches = list(re.finditer(pattern, modified_content, re.IGNORECASE | re.DOTALL))
                
                # 保留第一个，删除其他的
                if len(matches) > 1:
                    # 从后往前删除，避免索引变化
                    for match in reversed(matches[1:]):
                        modified_content = modified_content[:match.start()] + modified_content[match.end():]
                    
                    logger.info(f"删除了 {len(matches) - 1} 个重复的链接: {link}")
        
        return modified_content
    
    def fix_relative_links(self, content, base_url, links):
        """修复相对路径链接，添加基础URL前缀"""
        if not base_url or not links:
            return content
        
        modified_content = content
        
        for link in links:
            # 如果是相对路径（不以http开头）
            if not link.startswith(('http://', 'https://')):
                # 构建完整URL
                full_url = urljoin(base_url, link)
                
                # 替换内容中的相对路径
                escaped_link = re.escape(link)
                pattern = r'href=["\']' + escaped_link + r'["\']'
                replacement = f'href="{full_url}"'
                
                modified_content = re.sub(pattern, replacement, modified_content, flags=re.IGNORECASE)
                logger.info(f"修复相对路径: {link} -> {full_url}")
        
        return modified_content
    
    def clean_format_content(self, row):
        """清洗单行的formatContent字段"""
        content = row.get('formatContent', '')
        source_url = row.get('sourceUrl', '')
        
        if not content or pd.isna(content):
            return content
        
        # 查找附件链接
        links = self.find_attachment_links(content)
        
        if not links:
            return content
        
        original_content = content
        
        # 1. 删除重复链接
        content = self.remove_duplicate_links(content, links)
        
        # 2. 修复相对路径链接
        base_url = self.extract_base_url(source_url)
        if base_url:
            # 重新查找链接（因为可能有删除）
            updated_links = self.find_attachment_links(content)
            content = self.fix_relative_links(content, base_url, updated_links)
        
        # 记录修改
        if content != original_content:
            self.modified_count += 1
        
        return content
    
    def clean_csv_file(self, input_file, output_file=None):
        """清洗整个CSV文件"""
        if output_file is None:
            name, ext = os.path.splitext(input_file)
            output_file = f"{name}_cleaned{ext}"
        
        logger.info(f"开始清洗文件: {input_file}")
        
        # 读取CSV文件
        try:
            df = pd.read_csv(input_file, encoding='utf-8', low_memory=False)
            logger.info(f"成功读取 {len(df)} 条记录")
        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
            return False
        
        # 清洗formatContent字段
        self.processed_count = 0
        self.modified_count = 0
        
        for index, row in df.iterrows():
            self.processed_count += 1
            
            # 清洗formatContent
            cleaned_content = self.clean_format_content(row)
            df.at[index, 'formatContent'] = cleaned_content
            
            # 进度显示
            if self.processed_count % 100 == 0:
                logger.info(f"已处理 {self.processed_count}/{len(df)} 条记录，修改了 {self.modified_count} 条")
        
        # 保存清洗后的文件
        try:
            df.to_csv(output_file, encoding='utf-8', index=False)
            logger.info(f"清洗完成！已保存到: {output_file}")
            logger.info(f"总处理记录数: {self.processed_count}")
            logger.info(f"修改记录数: {self.modified_count}")
            return True
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return False
    
    def preview_changes(self, input_file, max_preview=5):
        """预览将要进行的修改"""
        logger.info(f"预览文件 {input_file} 的修改...")
        
        df = pd.read_csv(input_file, nrows=100)  # 只读取前100行进行预览
        
        preview_count = 0
        for index, row in df.iterrows():
            content = row.get('formatContent', '')
            links = self.find_attachment_links(content)
            
            if links:
                cleaned_content = self.clean_format_content(row)
                
                if content != cleaned_content:
                    preview_count += 1
                    print(f"\n预览 {preview_count}:")
                    print(f"标题: {row.get('title', '')}")
                    print(f"源URL: {row.get('sourceUrl', '')}")
                    print(f"发现链接: {links}")
                    print("原始内容片段:")
                    print(content[:200] + "..." if len(content) > 200 else content)
                    print("\n修改后内容片段:")
                    print(cleaned_content[:200] + "..." if len(cleaned_content) > 200 else cleaned_content)
                    print("-" * 80)
                    
                    if preview_count >= max_preview:
                        break


def main():
    """主函数"""
    cleaner = CSVCleaner()
    
    # CSV文件列表
    csv_files = [
        '新华妙笔-城市数据.csv',
        '新华妙笔-工种.csv'
    ]
    
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            print(f"\n{'='*60}")
            print(f"处理文件: {csv_file}")
            print(f"{'='*60}")
            
            # 预览修改
            cleaner.preview_changes(csv_file, max_preview=3)
            
            # 询问是否继续
            response = input(f"\n是否继续清洗文件 {csv_file}? (y/n): ")
            if response.lower() == 'y':
                # 执行清洗
                success = cleaner.clean_csv_file(csv_file)
                if success:
                    print(f"✅ 文件 {csv_file} 清洗完成")
                else:
                    print(f"❌ 文件 {csv_file} 清洗失败")
            else:
                print(f"⏭️ 跳过文件 {csv_file}")
        else:
            print(f"⚠️ 文件不存在: {csv_file}")


if __name__ == "__main__":
    main() 