import pandas as pd
import json
import traceback
import logging
import time
import uuid as uuid_lib
from datetime import datetime
import os
from pathlib import Path
from datetime import datetime
import re
import requests

from config import Config

# 配置日志
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL), format=Config.LOG_FORMAT)
logger = logging.getLogger(__name__)


def format_publish_time(time_str):
    """
    将publishTime格式化为yyyy-MM-dd HH:mm:ss格式

    Args:
        time_str: 原始时间字符串，可能的格式如：
                  - "2025-07-02"
                  - "2025-07-02 10:30:15"
                  - "2025/07/02"
                  - "2025年7月2日"
                  - "2025-07-02T10:30:15"

    Returns:
        str: 格式化后的时间字符串 "yyyy-MM-dd HH:mm:ss"
    """
    if not time_str or not isinstance(time_str, str):
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    time_str = time_str.strip()

    try:
        # 如果已经是完整格式，直接返回
        if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', time_str):
            return time_str

        # 处理 ISO 格式 (2025-07-02T10:30:15)
        if 'T' in time_str:
            time_str = time_str.split('T')[0] + ' ' + time_str.split('T')[1].split('+')[0].split('Z')[0]
            if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', time_str):
                return time_str[:19]  # 取前19个字符

        # 处理日期格式 (2025-07-02)
        if re.match(r'^\d{4}-\d{2}-\d{2}$', time_str):
            return time_str + " 00:00:00"

        # 处理斜杠格式 (2025/07/02 或 2025/7/2)
        if re.match(r'^\d{4}/\d{1,2}/\d{1,2}$', time_str):
            parts = time_str.split('/')
            year = parts[0]
            month = parts[1].zfill(2)
            day = parts[2].zfill(2)
            return f"{year}-{month}-{day} 00:00:00"

        # 处理带时间的斜杠格式 (2025/7/2 0:00)
        if re.match(r'^\d{4}/\d{1,2}/\d{1,2}\s+\d{1,2}:\d{2}$', time_str):
            date_part, time_part = time_str.split(' ', 1)
            parts = date_part.split('/')
            year = parts[0]
            month = parts[1].zfill(2)
            day = parts[2].zfill(2)
            time_parts = time_part.split(':')
            hour = time_parts[0].zfill(2)
            minute = time_parts[1].zfill(2)
            return f"{year}-{month}-{day} {hour}:{minute}:00"

        # 处理中文格式 (2025年7月2日)
        if '年' in time_str and '月' in time_str and '日' in time_str:
            # 提取年月日
            year_match = re.search(r'(\d{4})年', time_str)
            month_match = re.search(r'(\d{1,2})月', time_str)
            day_match = re.search(r'(\d{1,2})日', time_str)

            if year_match and month_match and day_match:
                year = year_match.group(1)
                month = month_match.group(1).zfill(2)
                day = day_match.group(1).zfill(2)
                return f"{year}-{month}-{day} 00:00:00"

        # 如果都不匹配，尝试用datetime解析
        dt = datetime.strptime(time_str, "%Y-%m-%d")
        return dt.strftime("%Y-%m-%d %H:%M:%S")

    except Exception as e:
        print(f"⚠️ 时间格式化失败: {time_str}, 错误: {str(e)}")
        # 返回当前时间作为默认值
        return ''


def push_web(data):
    print(f"uuid: {data['uuid']}")
    print(f"title: {data['title']}")
    print(f"content: {data['content'][:200]}")  # 显示前100字符
    print(f"formatContent: {data['formatContent'][:200]}")  # 显示前100字符
    print(f"source: {data['source']}")
    print(f"classification: {data['classification']}")
    print(f"label: {data['label']}")
    print(f"database: {data['database']}")
    print(f"sourceUrl: {data['sourceUrl']}")
    print(f"author: {data['author']}")
    print(f"compile: {data['compile']}")
    print(f"publishTime: {data['publishTime']}")

    body = [
        {
            "uuid": data['uuid'],
            "title": data['title'],
            "content": data['content'],
            "formatContent": data['formatContent'],
            "docType": 1,
            "source": data['source'],
            "classification": data['classification'],
            "label": data['label'],
            "database": data['database'],
            "sourceUrl": data['sourceUrl'],
            "author": data['author'],
            "compile": data['compile'],
            "publishTime": data['publishTime']
        }
    ]
    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
    }
    try:
        # re = requests.post(url="http://192.168.0.133:7720/import/gather-clean", json=body, headers=headers, timeout=160)
        re = requests.post(url="http://dev-writing.botsmart.cn/api/import/gather-clean", json=body, headers=headers, timeout=160)
        a = re.text
        data = json.loads(a)
        print(data)
    except Exception as e:
        traceback.print_exc()
        print('提交失败!!!')



class CSVDataPusher:
    """CSV数据推送器"""
    
    def __init__(self, config=None):
        """
        初始化推送器
        
        Args:
            config: 配置对象，如果为None则使用默认配置
        """
        self.config = config or Config
        self.batch_size = self.config.BATCH_SIZE
        self.delay_seconds = self.config.DELAY_SECONDS
        self.file_delay_seconds = self.config.FILE_DELAY_SECONDS
        self.test_mode = self.config.TEST_MODE
        self.max_retry_count = self.config.MAX_RETRY_COUNT
        self.retry_delay = self.config.RETRY_DELAY
        
        self.success_count = 0
        self.fail_count = 0
        self.total_count = 0
        
    def read_csv_file(self, file_path):
        """
        读取CSV文件
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            pandas.DataFrame: 读取的数据
        """
        try:
            logger.info(f"开始读取CSV文件: {file_path}")
            
            # 使用低内存模式读取大文件
            df = pd.read_csv(file_path, encoding='utf-8', low_memory=False)
            
            logger.info(f"成功读取CSV文件，共 {len(df)} 条数据")
            return df
            
        except Exception as e:
            logger.error(f"读取CSV文件失败: {file_path}, 错误: {str(e)}")
            traceback.print_exc()
            return None
    
    def prepare_data_for_push(self, row):
        """
        准备推送数据格式
        
        Args:
            row: DataFrame的一行数据
            
        Returns:
            dict: 格式化后的推送数据
        """
        try:
            def safe_str(value):
                """安全地将值转换为字符串，处理NaN值"""
                if pd.isna(value) or value is None:
                    return ''
                return str(value)
            
            # 处理UUID - 如果为空则生成新的
            row_uuid = row.get('uuid', '')
            if pd.isna(row_uuid) or not row_uuid:
                row_uuid = str(uuid_lib.uuid4())
            
            # 处理发布时间
            publish_time_raw = row.get('publishTime', '')
            publish_time = format_publish_time(safe_str(publish_time_raw))
            if not publish_time:
                publish_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 构建推送数据，使用safe_str处理所有字符串字段
            data = {
                "uuid": safe_str(row_uuid),
                "title": safe_str(row.get('title', '')),
                "content": safe_str(row.get('content', '')),
                "formatContent": safe_str(row.get('formatContent', '')),
                "docType": int(row.get('docType', 1)) if not pd.isna(row.get('docType', 1)) else 1,
                "source": safe_str(row.get('source', '')),
                "classification": safe_str(row.get('classification', '')),
                "label": safe_str(row.get('label', '')),
                "database": safe_str(row.get('database', '')),
                "sourceUrl": safe_str(row.get('sourceUrl', '')),
                "author": safe_str(row.get('author', '')),
                "compile": safe_str(row.get('compile', '')),
                "publishTime": publish_time
            }
            
            return data
            
        except Exception as e:
            logger.error(f"数据格式化失败: {str(e)}")
            traceback.print_exc()
            return None
    
    def push_single_data(self, data):
        """
        推送单条数据（支持重试机制）
        
        Args:
            data: 要推送的数据
            
        Returns:
            bool: 推送是否成功
        """
        # 过滤content长度小于10的数据
        content = data.get('content', '')
        if len(content.strip()) < 10:
            logger.info(f"跳过推送：content长度不足({len(content.strip())}字符) - {data.get('title', 'Unknown')}")
            return False
        
        for attempt in range(self.max_retry_count + 1):
            try:
                if self.test_mode:
                    # 测试模式，只是打印数据
                    logger.info(f"[测试模式] 准备推送数据: {data['title']}")
                    return True
                else:
                    # 实际推送
                    logger.info(f"推送数据: {data['title']} (尝试 {attempt + 1}/{self.max_retry_count + 1})")
                    push_web(data)
                    return True
                    
            except Exception as e:
                logger.error(f"推送数据失败 (尝试 {attempt + 1}/{self.max_retry_count + 1}): {data.get('title', 'Unknown')}, 错误: {str(e)}")
                
                if attempt < self.max_retry_count:
                    logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"推送数据最终失败: {data.get('title', 'Unknown')}")
                    traceback.print_exc()
                    return False
        
        return False
    
    def process_csv_file(self, file_path, max_records=None):
        """
        处理单个CSV文件
        
        Args:
            file_path: CSV文件路径
            max_records: 最大处理记录数，None表示处理全部
        """
        df = self.read_csv_file(file_path)
        if df is None:
            return
        
        # 限制处理记录数（用于测试）
        if max_records:
            df = df.head(max_records)
            logger.info(f"限制处理记录数为: {max_records}")
        
        total_records = len(df)
        self.total_count += total_records
        
        logger.info(f"开始处理文件: {file_path}, 共 {total_records} 条记录")
        
        # 分批处理
        for i in range(0, total_records, self.batch_size):
            batch_end = min(i + self.batch_size, total_records)
            batch_df = df.iloc[i:batch_end]
            
            logger.info(f"处理批次: {i//self.batch_size + 1}, 记录范围: {i+1}-{batch_end}")
            
            # 处理当前批次
            for index, row in batch_df.iterrows():
                try:
                    # 准备数据
                    push_data = self.prepare_data_for_push(row)
                    if push_data is None:
                        self.fail_count += 1
                        continue
                    
                    # 推送数据
                    if self.push_single_data(push_data):
                        self.success_count += 1
                    else:
                        self.fail_count += 1
                        
                except Exception as e:
                    logger.error(f"处理记录失败，行号: {index}, 错误: {str(e)}")
                    self.fail_count += 1
            
            # 批次间延迟
            if i + self.batch_size < total_records:
                logger.info(f"批次处理完成，等待 {self.delay_seconds} 秒...")
                time.sleep(self.delay_seconds)
    
    def process_all_csv_files(self, csv_files, max_records_per_file=None):
        """
        处理所有CSV文件
        
        Args:
            csv_files: CSV文件路径列表
            max_records_per_file: 每个文件最大处理记录数
        """
        start_time = time.time()
        
        logger.info(f"开始处理 {len(csv_files)} 个CSV文件")
        
        for i, file_path in enumerate(csv_files, 1):
            logger.info(f"处理第 {i}/{len(csv_files)} 个文件: {file_path}")
            
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                continue
            
            self.process_csv_file(file_path, max_records_per_file)
            
            # 文件间延迟
            if i < len(csv_files):
                logger.info(f"文件处理完成，等待 {self.file_delay_seconds} 秒...")
                time.sleep(self.file_delay_seconds)
        
        end_time = time.time()
        
        # 输出统计信息
        logger.info("=" * 50)
        logger.info("数据推送完成！统计信息:")
        logger.info(f"总处理记录数: {self.total_count}")
        logger.info(f"成功推送: {self.success_count}")
        logger.info(f"失败数量: {self.fail_count}")
        logger.info(f"成功率: {self.success_count/self.total_count*100:.2f}%" if self.total_count > 0 else "0%")
        logger.info(f"总耗时: {end_time - start_time:.2f} 秒")
        logger.info("=" * 50)


def main():
    """主函数"""
    # 获取CSV文件路径
    csv_files = Config.get_csv_files()
    
    # 创建推送器实例
    pusher = CSVDataPusher()
    
    # 处理所有CSV文件
    max_records = Config.MAX_RECORDS_PER_FILE if Config.TEST_MODE else None
    pusher.process_all_csv_files(csv_files, max_records_per_file=max_records)
    
    # 输出运行模式提示
    if Config.TEST_MODE:
        logger.info("当前运行在测试模式，如需实际推送请修改 config.py 中的 TEST_MODE = False")


if __name__ == "__main__":
    main() 