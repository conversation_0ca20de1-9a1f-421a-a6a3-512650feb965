import pandas as pd
import re

def find_attachments_in_csv(file_path, max_rows=1000):
    """查找CSV文件中的附件链接"""
    print(f"正在分析文件: {file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(file_path, nrows=max_rows)
    
    # 查找包含附件链接的记录
    attachment_records = []
    
    for idx, row in df.iterrows():
        content = str(row.get('formatContent', ''))
        
        # 查找href链接中包含附件扩展名的
        href_pattern = r'href=["\']([^"\']*\.(?:pdf|doc|docx|xls|xlsx)[^"\']*)["\']'
        href_links = re.findall(href_pattern, content, re.IGNORECASE)
        
        # 查找直接包含附件扩展名的文本
        text_pattern = r'[^<>\s]*\.(?:pdf|doc|docx|xls|xlsx)'
        text_attachments = re.findall(text_pattern, content, re.IGNORECASE)
        
        if href_links or text_attachments:
            attachment_records.append({
                'index': idx,
                'title': row.get('title', ''),
                'sourceUrl': row.get('sourceUrl', ''),
                'href_links': href_links,
                'text_attachments': text_attachments,
                'formatContent_sample': content[:500] + '...' if len(content) > 500 else content
            })
    
    return attachment_records

def main():
    # 分析两个CSV文件
    files = ['新华妙笔-城市数据.csv', '新华妙笔-工种.csv']
    
    for file_path in files:
        try:
            records = find_attachments_in_csv(file_path, max_rows=500000)
            
            print(f"\n文件 {file_path} 分析结果:")
            print(f"在前500条记录中找到 {len(records)} 条包含附件的记录\n")
            
            # 显示前几个示例
            for i, record in enumerate(records[:3]):
                print(f"示例 {i+1}:")
                print(f"  Index: {record['index']}")
                print(f"  Title: {record['title']}")
                print(f"  SourceUrl: {record['sourceUrl']}")
                print(f"  Href Links: {record['href_links']}")
                print(f"  Text Attachments: {record['text_attachments']}")
                print(f"  Content Sample: {record['formatContent_sample'][:200]}...")
                print("-" * 80)
                
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")

if __name__ == "__main__":
    main() 