# # -*- coding: utf-8 -*-
from apscheduler.schedulers.blocking import BlockingScheduler
import logging

from src.newscn import craw_newscn
from src.push.insert_push import InsertPush
from src.service.ResultService import Impl

logger = logging.getLogger(__name__)

def crawl_app():
    logger.info("start newcn crawl app")
    scheduler = BlockingScheduler(timezone='Asia/Shanghai')
    # scheduler.add_job(testnewcn, "cron", day_of_week='4', hour='16', minute="30", id='newcn')
    scheduler.add_job(craw_newscn, "interval", seconds=30, id='newcn')

    scheduler.start()

if __name__ == '__main__':
    crawl_app()
