import json
import sqlite3
import threading
import time
from queue import Queue



class InsertPush:
    push_event = threading.Event()
    push_queue = Queue(maxsize=50)
    con = sqlite3.connect(r'E:\全国各地政府单位文件\qggdzfdw.db', check_same_thread=False)
    cur = con.cursor()
    flag = True
    # 启动队列
    @staticmethod
    def start():
        InsertPush.push_event.set()
        push = threading.Thread(target=InsertPush.push)
        push.start()

    # 终止队列
    @staticmethod
    def shutdown():
        InsertPush.flag = False


    # 向队列中加入待处理的信息
    @staticmethod
    def put_queue_msg(msg):
        InsertPush.push_queue.put(msg)

    # 获取队列中待处理的信息
    @staticmethod
    def get_queue_msg():
        msg_info = InsertPush.push_queue.get()
        return msg_info

    @staticmethod
    def push():

        while InsertPush.flag:
            if InsertPush.push_event.is_set():
                # if InsertPush.push_queue.qsize() >= 500:
                if not InsertPush.push_queue.empty():
                    InsertPush.push_event.clear()
                    try:
                        msg_info = None
                        msg_info = InsertPush.get_queue_msg()
                        # todo 将你调用接口的数据代码补充在这里
                        # 注意当关闭程序时你这里的代码也会停止所以当发送成功你应该给该条数据打个标记以免程序意外退出后你不知道是否发送过
                        # 建议你把这个推送第三方的程序单独启动做个服务，这样保证它一直在运行

                        InsertPush.cur.execute(msg_info['sql'], msg_info['data'])
                        InsertPush.con.commit()
                        # dddd = [msg_info['data']]
                        # while len(dddd) < 500:
                        #     dddd.append(InsertPush.get_queue_msg()['data'])
                        # InsertPush.con.executemany(msg_info['sql'], dddd)
                        # InsertPush.con.commit()
                        print('添加成功')

                        # todo 将超时的错误作为异常抛出，如果直接会抛异常这里即不用手动写raise
                        # raise Exception('捕捉到的超时信息错误')
                    except Exception as e:
                        # 将该信息重新塞回对立等待下次处理
                        InsertPush.put_queue_msg(msg_info)
                    finally:
                        InsertPush.push_event.set()
                else:
                    time.sleep(1)
            else:
                time.sleep(1)