# # -*- coding: utf-8 -*-
import logging
import threading
from apscheduler.schedulers.blocking import BlockingScheduler
from src.service.ResultService import Impl
# logger = logging.getLogger(__name__)
from src.push.insert_push import InsertPush


def crawl_app():

    # logger.info("start crawl szxw")
    scheduler = BlockingScheduler(timezone='Asia/Shanghai')
    scheduler.add_job(Impl.push_data_bzy, "cron", day='*', hour='23', minute="30", misfire_grace_time=60, id='bzy_push',replace_existing=True)
    scheduler.add_job(Impl.pull_data_bzy, "cron", day='*', hour='3', minute="30", misfire_grace_time=60, id='bzy_pull',replace_existing=True)
    scheduler.start()


if __name__ == '__main__':
    print(111)
    InsertPush.start()
    # crawl_app()
    Impl.push_data_bzy()
    Impl.push_data_bzy_qx()
    Impl.push_data_bzy_rsbd()
    Impl.pull_data_bzy()
    Impl.pull_data_bzy_qx()
    # Impl.update_bzy()