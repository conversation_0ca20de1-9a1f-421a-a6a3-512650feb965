import re
import time
import traceback

from src.sqlite.sql import Sql
from src.upload.uploads import Uploads
from src.utils.Format import Format
from src.utils.PushDataUtils import Push

from src.service.bzy_data import crawl

class Impl:
    # 查重数据入库
    @classmethod
    def insert_datas(cls, data: dict):
        # if data['library'][-1] == '_':
        # if data['uuid'] != 'acf8a285f4a4f70bc908d868b7f7eb83':
        #     return None
        format_html, download = Format.post_html_format(content_html=data['content_html'], url=data['url'], title=data['title'])
        raw_content_html = data['content_html']
        if format_html:
            data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－\d+－  </span></p>\n', '', format_html)
            data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 政策解读(.*)</span></p>\n', '', data['content_html'])
            data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关政策(.*)</span></p>\n', '', data['content_html'])
            data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关报道(.*)</span></p>\n', '', data['content_html'])
            query_results = Sql.select_data_is_existence(uuid=data['uuid'])
            if len(query_results) < 1:
                try:
                    Sql.insert_data_local_repository(content=data, download=download, raw_content_html=raw_content_html)
                except Exception as e:
                    print('入库错误！！！')
                    Format.ding('入库错误！！！\n'+data['label']+'\n'+str(e))
            else:
                Sql.update_data_content_html(uuid=data['uuid'], content_html=data['content_html'], download=download, raw_content_html=raw_content_html)
                print('已修改: ' + data['uuid'])
            # elif query_results[0][0] != data['content_html'] and len(data['content_html']) > len(query_results[0][0]):
            #     Sql.update_data_content_html(uuid=data['uuid'], content_html=data['content_html'], download=download, raw_content_html=raw_content_html)
            #     print('已修改: ' + data['uuid'])
            # elif query_results[0][1] is None:
            #     Sql.update_data_raw_content_html(uuid=data['uuid'], raw_content_html=raw_content_html)
            #     print('已存在: '+data['uuid'])
        else:
            print('文本数据转换错误！跳过该条数据！！！')

    # 首次入库数据
    @classmethod
    def insert_data(cls, data: dict):
        # if data['library'][-1] == '_':
        query_results = Sql.select_data_is_existence(uuid=data['uuid'])
        if len(query_results) < 1:
            try:
                format_html, download = Format.post_html_format(content_html=data['content_html'], url=data['url'], title=data['title'])
                raw_content_html = data['content_html']
                if format_html:
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－\d+－  </span></p>\n', '', format_html)
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关阅读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">    相关阅读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关政策解读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关图片解读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">文件下载</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">打印 关闭 </span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">用微信扫描二维码</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">分享至好友和朋友圈    </span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">扫一扫在手机打开</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">>> 关闭</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('点击数：\d+次', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 政策解读(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关政策(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关报道(.*)</span></p>\n', '', data['content_html'])
                    Sql.insert_data_local_repository(content=data, download=download, raw_content_html=raw_content_html)
            except Exception as e:
                print('入库错误！！！')
                Format.ding('入库错误！！！\n' + data['label'] + '\n' + str(e))
        elif query_results[0][1] is None:
            Sql.update_data_raw_content_html(uuid=data['uuid'], raw_content_html=data['content_html'])
        elif query_results[0][1] != data['content_html'] and not '<html>' in query_results[0][0]:
            try:
                format_html, download = Format.post_html_format(content_html=data['content_html'], url=data['url'], title=data['title'])
                raw_content_html = data['content_html']
                if format_html:
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－\d+－  </span></p>\n', '', format_html)
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关阅读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">    相关阅读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关政策解读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关图片解读：(.*)</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">文件下载</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">打印 关闭 </span></p>\n', '',  data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">用微信扫描二维码</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">分享至好友和朋友圈    </span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">扫一扫在手机打开</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">>> 关闭</span></p>\n', '', data['content_html'])
                    data['content_html'] = re.sub('点击数：\d+次', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 政策解读(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关政策(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关报道(.*)</span></p>\n', '', data['content_html'])
                    Sql.update_data_content_html(uuid=data['uuid'], content_html=data['content_html'], download=download, raw_content_html=raw_content_html)
                    print('content_html不同！已修改: ' + data['uuid'])
            except Exception as e:
                print('入库错误！！！')
                Format.ding('入库错误！！！\n' + data['label'] + '\n' + str(e))
        else:
            print('已存在: '+data['uuid'])

    # 首次入库数据
    @classmethod
    def insert_data_qx(cls, data: dict):
        # if data['library'][-1] == '_':
        query_results = Sql.select_data_is_existence_qx(uuid=data['uuid'])
        if len(query_results) < 1:
            try:
                format_html, download = Format.post_html_format(content_html=data['content_html'], url=data['url'],
                                                                title=data['title'])
                raw_content_html = data['content_html']
                if format_html:
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－\d+－  </span></p>\n',
                        '', format_html)
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关阅读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">    相关阅读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关政策解读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关图片解读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">文件下载</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">打印 关闭 </span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">用微信扫描二维码</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">分享至好友和朋友圈    </span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">扫一扫在手机打开</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">>> 关闭</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub('点击数：\d+次', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 政策解读(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关政策(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关报道(.*)</span></p>\n', '', data['content_html'])
                    Sql.insert_data_local_repository_qx(content=data, download=download,
                                                        raw_content_html=raw_content_html)
            except Exception as e:
                print('入库错误！！！')
                Format.ding('入库错误！！！\n' + data['label'] + '\n' + str(e))
        elif query_results[0][1] is None:
            Sql.update_data_raw_content_html_qx(uuid=data['uuid'], raw_content_html=data['content_html'])

        elif query_results[0][1] != data['content_html'] and not '<html>' in query_results[0][0]:
            try:
                format_html, download = Format.post_html_format(content_html=data['content_html'], url=data['url'],
                                                                title=data['title'])
                raw_content_html = data['content_html']
                if format_html:
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－\d+－  </span></p>\n',
                        '', format_html)
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关阅读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">    相关阅读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关政策解读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关图片解读：(.*)</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">文件下载</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">打印 关闭 </span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">用微信扫描二维码</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">分享至好友和朋友圈    </span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">扫一扫在手机打开</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub(
                        '<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">>> 关闭</span></p>\n',
                        '', data['content_html'])
                    data['content_html'] = re.sub('点击数：\d+次', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 政策解读(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关政策(.*)</span></p>\n', '', data['content_html'])
                    # data['content_html'] = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">\| 相关报道(.*)</span></p>\n', '', data['content_html'])
                    Sql.update_data_content_html_qx(uuid=data['uuid'], content_html=data['content_html'],
                                                    download=download, raw_content_html=raw_content_html)
                    print('content_html不同！已修改: ' + data['uuid'])
            except Exception as e:
                print('入库错误！！！')
                Format.ding('入库错误！！！\n' + data['label'] + '\n' + str(e))
        else:
            print('已存在: ' + data['uuid'])

    @classmethod
    def pull_data_bzy(cls):
        crawls = crawl()
        datas = crawls.pull_data_all()
        datas_dl = []
        i = 0
        for data in datas:
            # if i > 12320 and i <32970:
            #     i = i + 1
            #     continue
            # i = i + 1
            try:
                if data['content_html'] is None or data['content_html'] == '' or data['content_html'] == ' ' \
                    or data['url'] is None or data['url'] == '' or data['url'] == ' '\
                    or data['title'] is None or data['title'] == '' or data['title'] == ' '\
                    or data['releaseTime'] is None or data['releaseTime'] == '' or data['releaseTime'] == ' ':
                    print('文本数据为空！跳过该条数据！！！')
                    continue
                if 'label1' in data and 'label2' in data:
                    if data['label1'] is None or data['label1'] == '' or data['label2'] is None or data['label2'] == '':
                        data['label'] = ''
                        print('label1,label2不完整, 跳过该数据！！！')
                        Format.ding('label1,label2不完整!!!' + data['label'])
                        continue
                    else:
                        data['label'] = data['label1'] + data['label2']
                if data['label'] is None or data['label'] == '':
                    print('label不完整, 跳过该数据！！！')
                    Format.ding('label不完整!!!' + data['label'])
                    continue
                if '<iframe' in data['content_html'] or '<audio' in data['content_html'] or '<video' in data['content_html']:
                    print('content 包含 \'iframe\'， \'audio\'， \'video\', 跳过该数据！！！')
                    continue
                if '..' in data['title']:
                    print('标题不完整, 跳过该数据！！！')
                    Format.ding('标题不完整!!!' + data['label'])
                    continue
                data["label"] = re.sub(',内蒙古,', ',内蒙古自治区,', data["label"])
                data["label"] = re.sub(',台湾,', ',台湾省,', data["label"])
                data["label"] = re.sub(',宁夏自治区,', ',宁夏回族自治区,', data["label"])
                data["label"] = re.sub(',香港,', ',香港特别行政区,', data["label"])
                data["label"] = re.sub(',澳门,', ',澳门特别行政区,', data["label"])
                data["label"] = re.sub(',新疆生产建设兵团,', ',新疆维吾尔自治区,', data["label"])
                data["uuid"] = Uploads.encryption(data["title"] + data["url"] + data["label"])
                cls.insert_data(data)
                # if '.pdf' in data["content_html"]:
                #     cls.insert_datas(data)
                # else:
                #     cls.insert_data(data)
            except Exception as e:
                traceback.print_exc()
                datas_dl.append(data)

        print('1')

    @classmethod
    def pull_data_bzy_rsbd(cls):
        crawls = crawl()
        print(111)
        datas = crawls.pull_data_all_rsbd()
        datas_dl = []
        i = 0
        for data in datas:
            # if i > 12320 and i <32970:
            #     i = i + 1
            #     continue
            # i = i + 1
            try:
                if data['content_html'] is None or data['content_html'] == '' or data['content_html'] == ' ' \
                    or data['url'] is None or data['url'] == '' or data['url'] == ' '\
                    or data['title'] is None or data['title'] == '' or data['title'] == ' '\
                    or data['releaseTime'] is None or data['releaseTime'] == '' or data['releaseTime'] == ' ':
                    print('文本数据为空！跳过该条数据！！！')
                    continue
                if 'label1' in data and 'label2' in data:
                    if data['label1'] is None or data['label1'] == '' or data['label2'] is None or data['label2'] == '':
                        data['label'] = ''
                        print('label1,label2不完整, 跳过该数据！！！')
                        Format.ding('label1,label2不完整!!!' + data['label'])
                        continue
                    else:
                        data['label'] = data['label1'] + data['label2']
                if data['label'] is None or data['label'] == '':
                    print('label不完整, 跳过该数据！！！')
                    Format.ding('label不完整!!!' + data['label'])
                    continue
                if '<iframe' in data['content_html'] or '<audio' in data['content_html'] or '<video' in data['content_html']:
                    print('content 包含 \'iframe\'， \'audio\'， \'video\', 跳过该数据！！！')
                    continue
                if '..' in data['title']:
                    print('标题不完整, 跳过该数据！！！')
                    Format.ding('标题不完整!!!' + data['label'])
                    continue
                data["label"] = re.sub(',内蒙古,', ',内蒙古自治区,', data["label"])
                data["label"] = re.sub(',台湾,', ',台湾省,', data["label"])
                data["label"] = re.sub(',宁夏自治区,', ',宁夏回族自治区,', data["label"])
                data["label"] = re.sub(',香港,', ',香港特别行政区,', data["label"])
                data["label"] = re.sub(',澳门,', ',澳门特别行政区,', data["label"])
                data["label"] = re.sub(',新疆生产建设兵团,', ',新疆维吾尔自治区,', data["label"])
                data["uuid"] = Uploads.encryption(data["title"] + data["url"] + data["label"])
                cls.insert_data(data)
                # if '.pdf' in data["content_html"]:
                #     cls.insert_datas(data)
                # else:
                #     cls.insert_data(data)
            except Exception as e:
                traceback.print_exc()
                datas_dl.append(data)

        print('1')



    @classmethod
    def pull_data_bzy_qx(cls):
        crawls = crawl()
        datas = crawls.pull_data_all_qx()
        datas_dl = []
        i = 0
        for data in datas:
            # if i > 12320 and i <32970:
            #     i = i + 1
            #     continue
            # i = i + 1
            try:
                if data['content_html'] is None or data['content_html'] == '' or data['content_html'] == ' ' \
                    or data['url'] is None or data['url'] == '' or data['url'] == ' '\
                    or data['title'] is None or data['title'] == '' or data['title'] == ' '\
                    or data['releaseTime'] is None or data['releaseTime'] == '' or data['releaseTime'] == ' ':
                    print('文本数据为空！跳过该条数据！！！')
                    continue
                if 'label1' in data and 'label2' in data:
                    if data['label1'] is None or data['label1'] == '' or data['label2'] is None or data['label2'] == '':
                        data['label'] = ''
                        print('label1,label2不完整, 跳过该数据！！！')
                        Format.ding('label1,label2不完整!!!' + data['label'])
                        continue
                    else:
                        data['label'] = data['label1'] + data['label2']
                if data['label'] is None or data['label'] == '':
                    print('label不完整, 跳过该数据！！！')
                    Format.ding('label不完整!!!' + data['label'])
                    continue
                if '<iframe' in data['content_html'] or '<audio' in data['content_html'] or '<video' in data['content_html']:
                    print('content 包含 \'iframe\'， \'audio\'， \'video\', 跳过该数据！！！')
                    continue
                if '..' in data['title']:
                    print('标题不完整, 跳过该数据！！！')
                    Format.ding('标题不完整!!!' + data['label'])
                    continue
                data["label"] = re.sub(',内蒙古,', ',内蒙古自治区,', data["label"])
                data["label"] = re.sub(',台湾,', ',台湾省,', data["label"])
                data["label"] = re.sub(',宁夏自治区,', ',宁夏回族自治区,', data["label"])
                data["label"] = re.sub(',香港,', ',香港特别行政区,', data["label"])
                data["label"] = re.sub(',澳门,', ',澳门特别行政区,', data["label"])
                data["label"] = re.sub(',新疆生产建设兵团,', ',新疆维吾尔自治区,', data["label"])
                data["uuid"] = Uploads.encryption(data["title"] + data["url"] + data["label"])
                cls.insert_data_qx(data)
                # if '.pdf' in data["content_html"]:
                #     cls.insert_datas(data)
                # else:
                #     cls.insert_data(data)
            except Exception as e:
                traceback.print_exc()
                datas_dl.append(data)

        print('1')

    @classmethod
    def push_data_bzy(cls):
        push_datas = []
        datas = Sql.select_data_no_push()
        for d in datas:
            data = {
                "uuid": d[0],
                "title": d[1],
                # "formatContent": re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>', '', d[2]),
                "formatContent": d[2],
                "sourceName": d[3],
                "library": d[4],
                "label": d[5],
                "url": d[6],
                "releaseTime": d[7],
                "lab": d[8]
            }
            push_datas.append(data)
        Push.send_ad_datas(push_datas)
        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())+': 已完成数据推送')

    @classmethod
    def push_data_bzy_qx(cls):
        push_datas = []
        datas = Sql.select_data_no_push_qx()
        for d in datas:
            data = {
                "uuid": d[0],
                "title": d[1],
                # "formatContent": re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>', '', d[2]),
                "formatContent": d[2],
                "sourceName": d[3],
                "library": d[4],
                "label": d[5],
                "url": d[6],
                "location": d[7],
                "releaseTime": d[8],
                "lab": d[9]
            }
            push_datas.append(data)
        Push.send_ad_datas_qx(push_datas)
        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())+': 已完成数据推送')

    @classmethod
    def push_data_bzy_rsbd(cls):
        push_datas = []
        datas = Sql.select_data_no_push_rsbd()
        for d in datas:
            data = {
                "uuid": d[0],
                "title": d[1],
                # "formatContent": re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>', '', d[2]),
                "formatContent": d[2],
                "sourceName": d[3],
                "library": d[4],
                "label": d[5],
                "url": d[6],
                "releaseTime": d[7],
                "lab": d[8]
            }
            push_datas.append(data)
        Push.send_ad_datas_rsbd(push_datas)
        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())+': 已完成数据推送')

    @classmethod
    def update_bzy(cls):
        # datas = Sql.select_data_content_html()
        datas = Sql.select_data_raw_content_html()
        for data in datas:
            # if '<font' in data[3] or '<center>' in data[3]:
            #     format_html, download = Format.post_html_format(content_html=re.sub('font', 'div', re.sub('<center>|</center>', '', data[3])), url=data[2], title=data[1])
            # else:
            #     format_html = data[4]
            # # if '相关阅读：' in data[1]:
            # if format_html is None:
            #     format_html = data[4]
            # print(data[0])
            # # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关阅读：(.*)</span></p>\n', '', data[1])
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关阅读：(.*)</span></p>\n', '', format_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">政策原文:(.*)</span></p>\n', '', format_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">    相关阅读：(.*)</span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关政策解读：(.*)</span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">相关图片解读：(.*)</span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">文件下载</span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">打印 关闭 </span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">用微信扫描二维码</span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">分享至好友和朋友圈    </span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">扫一扫在手机打开</span></p>\n', '', new_content_html)
            # new_content_html = re.sub('<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">>> 关闭</span></p>\n', '', new_content_html)
            # new_content_html = re.sub('点击数：\d+次', '', new_content_html)
            label = re.sub(',疆维吾尔自治区,', ',新疆维吾尔自治区,', data[1])
            # releaseTime = re.sub('2025', '2001', data[1])
            # new_content_html = re.sub('【 字体：大 中 小 】 ', '', new_content_html)
            # Sql.updata_data_content_html(uuid=data[0], content_html=new_content_html, lab='1')
            Sql.updata_data_label(uuid=data[0], label=label)
            # Sql.updata_data_content_html(uuid=data[0], content_html=new_content_html, lab=data[2])
            # Sql.updata_data_releaseTime(uuid=data[0], releaseTime=releaseTime)
        # datas = Sql.select_data_all()
        # for d in datas:
        #     # if datas.index(d) < 30227:
        #     #     continue
        #     new_uuid = Uploads.encryption(d[1] + d[2] + d[3])
        #     print(new_uuid)
        #     query_results = Sql.select_data_is_existences(new_uuid=new_uuid, uuid=d[0])
        #     if len(query_results) > 1:
        #         Sql.delete_data_uuid(uuid=d[0])
        #     else:
        #         Sql.update_data_uuid(new_uuid=new_uuid, uuid=d[0])
        print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())+': 已完成数据更新')

