import base64
import datetime
import sqlite3
import threading
from typing import Optional

from src.push.insert_push import InsertPush
lock = threading.Lock()

class Sql:
    con = sqlite3.connect(r'E:\全国各地政府单位文件\qggdzfdw.db', check_same_thread=False)
    cur = con.cursor()

    # 处理sql语句
    @staticmethod
    def handle_sql(sql: Optional[str],
                   data: Optional):
        # 把数据推到队列中
        body_data = {
            "sql": sql,
            "data": data}
        InsertPush.put_queue_msg(body_data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()

    # 添加本地入库数据_未入主库
    @staticmethod
    def insert_data_local_repository(content: Optional[dict],
                                     download: Optional[str],
                                     raw_content_html: Optional[str]):
        sql = 'insert into qggdzfdw (uuid,title,content_html,source_name,library,label,url,releaseTime,receipt,lab,raw_content_html) values(?,?,?,?,?,?,?,?,?,?,?)'
        if download == 'fail' or not '<html>' in content['content_html']:
            lab = 4
        else:
            lab = 0
        data = [content['uuid'], content['title'], content['content_html'],
                content['source_name'], content['library'], content['label'], content['url'],
                content['releaseTime'], '', lab, raw_content_html]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()
        # Sql.cur.close()
        # Sql.con.close()

    @staticmethod
    def insert_data_local_repository_qx(content: Optional[dict],
                                        download: Optional[str],
                                        raw_content_html: Optional[str]):
        sql = 'insert into qggdzfdw_qx (uuid,title,content_html,source_name,library,label,url,releaseTime,receipt,lab,raw_content_html,location) values(?,?,?,?,?,?,?,?,?,?,?,?)'
        if download == 'fail' or not '<html>' in content['content_html']:
            lab = 4
        else:
            lab = 0
        data = [content['uuid'], content['title'], content['content_html'],
                content['source_name'], content['library'], content['label'], content['url'],
                content['releaseTime'], '', lab, raw_content_html, content['location']]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()
        # Sql.cur.close()
        # Sql.con.close()

    # 添加本地入库数据_已入主库
    @staticmethod
    def insert_data_repository(content: Optional[dict]):
        sql = 'insert into qggdzfdw (uuid,source_name,collection_url,url,title,subtitle,content_html,classify,library,label,attachment_url,releaseTime,createTime,ext,receipt,lab) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)'
        data = [content['uuid'], content['source_name'], content['collection_url'],
                content['url'], content['title'], content['subtitle'], content['content_html'],
                content['classify'], content['library'], content['label'], content['attachment_url'],
                content['releaseTime'], content['createTime'], content['ext'], content['receipt'], content['lab']]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()
        # Sql.cur.close()
        # Sql.con.close()

    # 修改入库失败数据
    @staticmethod
    def update_data_repository(uuid: Optional[str],
                               receipt: Optional[str],
                               lab: Optional[int],
                               push_time: Optional[str]):
        sql = 'update qggdzfdw set receipt = ?, lab = ?, push_time = ? where uuid = ?'
        data = [receipt, lab, push_time, uuid]
        Sql.handle_sql(sql=sql, data=data)

    @staticmethod
    def update_data_repository_qx(uuid: Optional[str],
                                  receipt: Optional[str],
                                  lab: Optional[int],
                                  push_time: Optional[str]):
        sql = 'update qggdzfdw_qx set receipt = ?, lab = ?, push_time = ? where uuid = ?'
        data = [receipt, lab, push_time, uuid]
        Sql.handle_sql(sql=sql, data=data)

    # 修改入库失败数据
    @staticmethod
    def update_data_content_html(uuid: Optional[str],
                                 content_html: Optional[str],
                                 download: Optional[str],
                                 raw_content_html: Optional[str]):
        if download == 'fail' or not '<html>' in content_html:
            lab = 4
        else:
            lab = 0
        sql = 'update qggdzfdw set content_html = ?, receipt = ?, lab = ?, raw_content_html = ? where uuid = ?'
        data = [content_html, '', lab, raw_content_html, uuid]
        Sql.handle_sql(sql=sql, data=data)

    @staticmethod
    def update_data_content_html_qx(uuid: Optional[str],
                                    content_html: Optional[str],
                                    download: Optional[str],
                                    raw_content_html: Optional[str]):
        if download == 'fail' or not '<html>' in content_html:
            lab = 4
        else:
            lab = 0
        sql = 'update qggdzfdw_qx set content_html = ?, receipt = ?, lab = ?, raw_content_html = ? where uuid = ?'
        data = [content_html, '', lab, raw_content_html, uuid]
        Sql.handle_sql(sql=sql, data=data)

    @staticmethod
    def update_data_raw_content_html(uuid: Optional[str],
                                     raw_content_html: Optional[str]):
        sql = 'update qggdzfdw set raw_content_html = ? where uuid = ?'
        data = [raw_content_html, uuid]
        Sql.handle_sql(sql=sql, data=data)

    @staticmethod
    def update_data_raw_content_html_qx(uuid: Optional[str],
                                        raw_content_html: Optional[str]):
        sql = 'update qggdzfdw_qx set raw_content_html = ? where uuid = ?'
        data = [raw_content_html, uuid]
        Sql.handle_sql(sql=sql, data=data)

    # 修改入库数据uuid
    @staticmethod
    def update_data_uuid(new_uuid: Optional[str],
                         uuid: Optional[str]):
        sql = 'update qggdzfdw set uuid = ? where uuid = ?'
        data = [new_uuid, uuid]
        Sql.handle_sql(sql=sql, data=data)

    # 修改入库数据uuid
    @staticmethod
    def delete_data_uuid(uuid: Optional[str]):
        sql = 'delete from qggdzfdw where uuid = ?'
        data = [uuid]
        Sql.handle_sql(sql=sql, data=data)

    # 修改入库成功数据
    @staticmethod
    def update_data_content_html_su(uuid: Optional[str],
                                    receipt: Optional[str],
                                    content_html: Optional[str],
                                    lab: Optional[int]):
        sql = 'update qggdzfdw set content_html = ?, receipt = ?, lab = ? where uuid = ?'
        data = [content_html, receipt, lab, uuid]
        Sql.handle_sql(sql=sql, data=data)

    # 修改采集失败数据
    @staticmethod
    def update_content(uuid: Optional[str],
                       content_html: Optional[str],
                       ext: Optional[str],
                       attachment_url: Optional[str]):
        sql = 'update qggdzfdw set content_html = ?, ext = ?, attachment_url = ?, lab = ? where uuid = ?'
        data = [content_html, ext, attachment_url, 0, uuid]
        Sql.handle_sql(sql=sql, data=data)

    # 修改 图片上传失败 数据
    @staticmethod
    def update_data_fail_upload(image_id: Optional[str],
                                url: Optional[str],
                                image_path: Optional[str],
                                error_msg: Optional[str]):
        sql = 'update images_audit set obs_url = ?, image = ?, result = ?, time = ?, lab = ?, error_msg = ? where image_id = ? and url = ?'
        data = ['', image_path, '', '', Sql.key_word['图片上传失败'], error_msg, image_id, url]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()

    # 修改 图片审核失败 数据
    @staticmethod
    def update_data_fail_audit(image_id: Optional[str],
                               url: Optional[str],
                               obs_url: Optional[str],
                               error_msg: Optional[str]):
        sql = 'update images_audit set obs_url = ?, image = ?, result = ?, time = ?, lab = ?, error_msg = ? where image_id = ? and url = ?'
        data = [obs_url, '', '', '', Sql.key_word['图片审核失败'], error_msg, image_id, url]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()

    # 修改 图片审核成功 数据
    @staticmethod
    def update_data_ok(image_id: Optional[str],
                       url: Optional[str],
                       obs_url: Optional[str],
                       result: Optional[str]):
        time = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
        sql = 'update images_audit set obs_url = ?, image = ?, result = ?, time = ?, lab = ?, error_msg = ? where image_id = ? and url = ?'
        data = [obs_url, '', result, time, Sql.key_word['图片审核成功'], '', image_id, url]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()

    # 修改 视频送审成功 数据
    @staticmethod
    def update_data_video_ok(video_id: Optional[str],
                             url: Optional[str],
                             obs_url: Optional[str],
                             taskid: Optional[str]):
        sql = 'update videos_audit set obs_url = ?, taskid = ?, result = ?, time = ?, lab = ?, error_msg = ? where video_id = ? and url = ?'
        data = [obs_url, taskid, '', '', Sql.key_word['视频送审成功'], '', video_id, url]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()

    # 修改 视频审核成功 数据
    @staticmethod
    def update_data_video_result_ok(taskId: Optional[str],
                                    result: Optional[str]):
        time = datetime.datetime.strftime(datetime.datetime.now(), '%Y-%m-%d')
        sql = 'update videos_audit set result = ?, time = ?, lab = ?, error_msg = ? where taskId = ?'
        data = [result, time, Sql.key_word['图片审核成功'], '', taskId]
        Sql.handle_sql(sql=sql, data=data)
        # Sql.cur.execute(sql, data)
        # Sql.con.commit()


    # 查询uuid是否存在
    @staticmethod
    def select_data_is_existence(uuid: Optional[str]):
        sql = 'select content_html, raw_content_html from qggdzfdw where uuid = ?'
        data = [uuid]
        # Sql.handle_sql(sql=sql, data=data)
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql, data).fetchall()
            # do something
        finally:
            lock.release()
        # Sql.con.commit()
        return fetch

    @staticmethod
    def select_data_is_existence_qx(uuid: Optional[str]):
        sql = 'select content_html, raw_content_html from qggdzfdw_qx where uuid = ?'
        data = [uuid]
        # Sql.handle_sql(sql=sql, data=data)
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql, data).fetchall()
            # do something
        finally:
            lock.release()
        # Sql.con.commit()
        return fetch

    @staticmethod
    def select_data_is_existences(new_uuid: Optional[str],
                                  uuid: Optional[str]):
        sql = 'select content_html from qggdzfdw where uuid = ? or uuid = ?'
        data = [new_uuid, uuid]
        # Sql.handle_sql(sql=sql, data=data)
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql, data).fetchall()
            # do something
        finally:
            lock.release()
        # Sql.con.commit()
        return fetch

    @staticmethod
    def updata_data_content_html(uuid: Optional[str],
                                 content_html: Optional[str],
                                 lab: Optional[str]):
        if lab == '1' or lab == '0':
            sql = 'update qggdzfdw set content_html = ?, lab = 0 where uuid = ?'
            data = [content_html, uuid]
        else:
            sql = 'update qggdzfdw set content_html = ?, lab = 4 where uuid = ?'
            data = [content_html, uuid]
        Sql.handle_sql(sql=sql, data=data)

    @staticmethod
    def updata_data_releaseTime(uuid: Optional[str],
                                releaseTime: Optional[str]):
        sql = 'update qggdzfdw set releaseTime = ? where uuid = ?'
        data = [releaseTime, uuid]
        Sql.handle_sql(sql=sql, data=data)

    @staticmethod
    def updata_data_label(uuid: Optional[str],
                          label: Optional[str]):
        sql = 'update qggdzfdw set label = ?, lab = 0 where uuid = ?'
        data = [label, uuid]
        Sql.handle_sql(sql=sql, data=data)

    @staticmethod
    def select_data_raw_content_html():
        data = ['省级文件,疆维吾%']
        # sql = "select uuid,title,url,raw_content_html,content_html from qggdzfdw where label LIKE ? and library not like '地方人事任免_%' and lab = 1"
        sql = "select uuid,label from qggdzfdw where lab = 1 and label like ? and library not like '地方人事任免_%'"
        # sql = 'select uuid,title,content_html,source_name,library,label,url,releaseTime,lab from qggdzfdw where content_html LIKE \'%<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>%\' LIMIT 30000'
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql, data).fetchall()
        finally:
            lock.release()
        return fetch

    @staticmethod
    def select_data_content_html():
        data = ['省级文件,西藏%']
        sql = "select uuid,content_html,lab from qggdzfdw where label LIKE ? and library not like '地方人事任免_%'"
        # sql = 'select uuid,title,content_html,source_name,library,label,url,releaseTime,lab from qggdzfdw where content_html LIKE \'%<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>%\' LIMIT 30000'
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql, data).fetchall()
        finally:
            lock.release()
        return fetch

    # 查询uuid和library数据
    @staticmethod
    def select_data_library(uuid: Optional[str]):
        sql = 'select library from qggdzfdw where uuid = ?'
        data = [uuid]
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql, data).fetchall()
            # do something
        finally:
            lock.release()
        return fetch

    # 查询所有未入主库数据
    @staticmethod
    def select_data_no_push():
        sql = 'select uuid,title,content_html,source_name,library,label,url,releaseTime,lab from qggdzfdw where lab = 0 and library not like "%人事任免_%" LIMIT 60000'
        # sql = 'select uuid,title,content_html,source_name,library,label,url,releaseTime,lab from qggdzfdw where content_html LIKE \'%<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>%\' LIMIT 30000'
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql).fetchall()
        finally:
            lock.release()
        return fetch

    # 查询所有未入主库数据
    @staticmethod
    def select_data_no_push_qx():
        sql = 'select uuid,title,content_html,source_name,library,label,url,location,releaseTime,lab from qggdzfdw_qx where lab = 0 and library not like "%人事任免_%" LIMIT 60000'
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql).fetchall()
        finally:
            lock.release()
        return fetch

    # 查询所有未入主库数据_人事变动
    @staticmethod
    def select_data_no_push_rsbd():
        sql = 'select uuid,title,content_html,source_name,library,label,url,releaseTime,lab from qggdzfdw where lab = 0 and library like "%人事任免_%" LIMIT 60000'
        # sql = 'select uuid,title,content_html,source_name,library,label,url,releaseTime,lab from qggdzfdw where content_html LIKE \'%<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>%\' LIMIT 30000'
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql).fetchall()
        finally:
            lock.release()
        return fetch

    # 查询所有数据
    @staticmethod
    def select_data_all():
        sql = 'select uuid,title,url,label from qggdzfdw'
        # sql = 'select uuid,title,content_html,source_name,library,label,url,releaseTime,lab from qggdzfdw where content_html LIKE \'%<p>　　<span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">－1－  </span></p>%\' LIMIT 30000'
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql).fetchall()
        finally:
            lock.release()
        return fetch

    # 查询所有content_html为空的数据
    @staticmethod
    def select_content_empty():
        sql = 'select uuid,url from qggdzfdw where content_html = ""'
        try:
            lock.acquire(True)
            fetch = Sql.cur.execute(sql).fetchall()
        finally:
            lock.release()
        return fetch

    # 查询 所有未审核数据的id和url
    @staticmethod
    def select_no_audit_all():
        sql = 'select image_id, url from images_audit where lab = 0'
        fetch = Sql.cur.execute(sql).fetchall()
        return fetch

    # 查询 所有未审核视频数据的id和url
    @staticmethod
    def select_no_audit_video_all():
        sql = 'select video_id, url from videos_audit where lab = 0'
        fetch = Sql.cur.execute(sql).fetchall()
        return fetch

    # 查询 所有送审视频数据的id和url
    @staticmethod
    def select_to_audit_video_all():
        sql = 'select taskId from videos_audit where lab = 5'
        fetch = Sql.cur.execute(sql).fetchall()
        return fetch

    # 查询 所有审核失败数据的id和url
    @staticmethod
    def select_audit_fail_all(result):
        sql = 'select image_id, url from images_audit where result = ?'
        data = [result]
        fetch = Sql.cur.execute(sql, data).fetchall()
        return fetch
