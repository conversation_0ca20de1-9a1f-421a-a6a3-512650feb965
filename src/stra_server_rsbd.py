# # -*- coding: utf-8 -*-
from apscheduler.schedulers.blocking import BlockingScheduler
import logging

from src.push.insert_push import InsertPush
from src.service.ResultService import Impl

logger = logging.getLogger(__name__)

def crawl_app():
    logger.info("start crawl app")
    scheduler = BlockingScheduler(timezone='Asia/Shanghai')
    scheduler.add_job(Impl.pull_data_bzy_rsbd, "cron", day_of_week='4', hour='16', minute="30", id='pull_data_bzy_rsbd')
    scheduler.add_job(Impl.push_data_bzy_rsbd, "cron", day_of_week='4', hour='18', minute="30", id='push_data_bzy_rsbd')
    scheduler.start()

if __name__ == '__main__':
    InsertPush.start()
    crawl_app()
