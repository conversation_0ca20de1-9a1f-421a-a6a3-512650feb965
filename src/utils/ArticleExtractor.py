#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文章内容提取工具类
功能：传入详情URL链接，解析返回text和html内容
"""

import requests
import re
import time
import threading
import os
import uuid
import hashlib
from urllib.parse import urljoin, urlparse
from newspaper import Article
from bs4 import BeautifulSoup
import trafilatura
from gne import GeneralNewsExtractor

# 导入存储管理器
try:
    from .storage_manager import get_storage_manager
except ImportError:
    # 兼容直接运行的情况
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from storage_manager import get_storage_manager

# 全局session和缓存
_global_session = None
_session_lock = threading.Lock()
_html_cache = {}
_cache_lock = threading.Lock()

class ArticleExtractor:
    """文章内容提取器"""
    
    def __init__(self, timeout=(10, 30), enable_cache=True, upload_to_minio=True, config_path="config/config.yml"):
        """
        初始化提取器
        :param timeout: 超时设置(连接超时, 读取超时)
        :param enable_cache: 是否启用缓存
        :param upload_to_minio: 是否上传图片到MinIO（默认开启）
        :param config_path: 配置文件路径
        """
        self.timeout = timeout
        self.enable_cache = enable_cache
        self.upload_to_minio = upload_to_minio
        
        # 初始化存储管理器
        self.storage_manager = get_storage_manager(config_path)
    
    def _get_session(self):
        """获取HTTP session"""
        global _global_session
        
        if _global_session is None:
            with _session_lock:
                if _global_session is None:
                    _global_session = requests.Session()
                    # 配置HTTP适配器
                    adapter = requests.adapters.HTTPAdapter(
                        pool_connections=10,
                        pool_maxsize=20,
                        max_retries=3
                    )
                    _global_session.mount('http://', adapter)
                    _global_session.mount('https://', adapter)
                    
                    # 设置请求头
                    _global_session.headers.update({
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.54 Safari/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Connection': 'keep-alive'
                    })
        
        return _global_session
    
    def _fetch_html(self, url):
        """
        获取网页HTML内容
        :param url: 目标URL
        :return: (html_content, error_message)
        """
        try:
            # 检查缓存
            if self.enable_cache:
                with _cache_lock:
                    if url in _html_cache:
                        return _html_cache[url], None
            
            session = self._get_session()
            response = session.get(url, timeout=self.timeout, verify=False, allow_redirects=True)
            response.raise_for_status()
            
            # 检测编码
            response.encoding = response.apparent_encoding or 'utf-8'
            html_content = response.text
            
            # 缓存结果
            if self.enable_cache:
                with _cache_lock:
                    _html_cache[url] = html_content
                    # 限制缓存大小
                    if len(_html_cache) > 50:
                        oldest_key = next(iter(_html_cache))
                        del _html_cache[oldest_key]
            
            return html_content, None
            
        except requests.exceptions.Timeout:
            return None, "网络请求超时"
        except requests.exceptions.ConnectionError:
            return None, "网络连接错误"
        except requests.exceptions.HTTPError as e:
            return None, f"HTTP错误: {e.response.status_code if e.response else 'unknown'}"
        except Exception as e:
            return None, f"请求异常: {str(e)}"
    
    def _clean_text_content(self, text):
        """
        清理文本内容，移除不需要的信息
        :param text: 原始文本
        :return: 清理后的文本
        """
        if not text:
            return text
        
        # 定义需要过滤的关键词模式
        filter_patterns = [
            # 附件相关
            r'.*附件[:：].*',
            r'.*相关文件[:：].*',
            r'.*下载附件.*',
            r'.*附件下载.*',
            r'.*点击下载.*',
            r'.*文件下载.*',

            # 备案号相关
            r'.*备案号[:：].*',
            r'.*ICP备案.*',
            r'.*备\d+号.*',
            r'.*网站备案.*',
            r'.*京ICP备.*',
            r'.*政府网站标识码.*',

            # 举报电话相关
            r'.*举报电话[:：].*',
            r'.*违法和不良信息举报.*',
            r'.*本网站违法.*',
            r'.*举报邮箱[:：].*',
            r'.*监督电话[:：].*',
            r'.*投诉电话[:：].*',

            # 视频相关
            r'.*播放视频.*',
            r'.*视频播放.*',
            r'.*观看视频.*',
            r'.*点击播放.*',
            r'.*视频加载.*',
            r'.*全屏播放.*',
            r'.*暂停播放.*',
            r'.*视频内容.*',
            r'.*MP4.*',
            r'.*\.mp4.*',
            r'.*\.avi.*',
            r'.*\.mov.*',
            r'.*\.wmv.*',

            # JavaScript/脚本相关
            r'.*<script.*',
            r'.*</script>.*',
            r'.*javascript:.*',
            r'.*function\s*\(.*',
            r'.*var\s+.*=.*',
            r'.*document\..*',
            r'.*window\..*',
            r'.*onclick.*=.*',
            r'.*onload.*=.*',

            # 其他无关信息
            r'.*网站地图.*',
            r'.*联系我们.*',
            r'.*网站声明.*',
            r'.*免责声明.*',
            r'.*版权声明.*',
            r'.*网站导航.*',
            r'.*站点地图.*',
            r'.*友情链接.*',
            r'.*技术支持.*',
            r'.*版权所有.*',
            r'.*All Rights Reserved.*',
            r'.*Copyright.*',

            # 社交媒体分享
            r'.*分享到[:：].*',
            r'.*微信分享.*',
            r'.*微博分享.*',
            r'.*QQ分享.*',
            r'.*分享按钮.*',
            r'.*分享让更多人看到.*',

            # 页面操作相关
            r'.*打印本页.*',
            r'.*关闭窗口.*',
            r'.*返回顶部.*',
            r'.*页面纠错.*',
            r'.*【纠错】.*',
            r'.*\[纠错\].*',
            r'.*纠错.*',
            r'.*【.*纠错.*】.*',
            r'.*\[.*纠错.*\].*',
        ]
        
        # 逐行过滤
        lines = text.split('\n')
        filtered_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否需要过滤
            should_filter = False
            for pattern in filter_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    should_filter = True
                    break
            
            if not should_filter:
                filtered_lines.append(line)
        
        # 清理多余的换行符
        cleaned_text = '\n'.join(filtered_lines)
        cleaned_text = re.sub(r'\n{2,}', '\n', cleaned_text)
        
        return cleaned_text.strip()
    
    def _deep_clean_javascript(self, text):
        """
        深度清理JavaScript代码和配置
        :param text: 原始文本
        :return: 清理后的文本
        """
        if not text:
            return text
        
        # 🚫 移除JSON配置对象 (如视频播放器配置) - 增强版
        # 匹配包含常见配置属性的JSON对象
        json_patterns = [
            r'\{[^{}]*"autoplay"[^{}]*\}',  # 包含autoplay的配置
            r'\{[^{}]*"poster"[^{}]*\}',    # 包含poster的配置
            r'\{[^{}]*"width"\s*:\s*\d+[^{}]*\}',  # 包含width数值的配置
            r'\{[^{}]*"height"\s*:\s*\d+[^{}]*\}', # 包含height数值的配置
            r'\{[^{}]*"src"\s*:\s*"[^"]*\.(mp4|avi|mov|wmv)[^"]*"[^{}]*\}', # 包含视频源的配置
            r'\{[^{}]*"pausePosterEnabled"[^{}]*\}', # 包含暂停海报的配置
            r'\{[^{}]*"style"\s*:\s*"cover"[^{}]*\}', # 包含样式配置
            # 增强：匹配更复杂的嵌套配置
            r'\{[^{}]*\{[^{}]*"style"[^{}]*\}[^{}]*\}', # 嵌套的style配置
            r'\{[^{}]*autoplay\s*:\s*\w+[^{}]*\}', # 不带引号的autoplay
            r'\{[^{}]*width\s*:\s*\d+[^{}]*\}',   # 不带引号的width
            r'\{[^{}]*height\s*:\s*\d+[^{}]*\}',  # 不带引号的height
        ]
        
        for pattern in json_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE | re.DOTALL)
        
        # 🚫 移除JavaScript函数调用和对象
        js_call_patterns = [
            r'\w+\s*\(\s*\{[^}]*\}\s*\)\s*;?',  # 函数调用 funcName({...});
            r'new\s+\w+\s*\([^)]*\)\s*;?',      # new 构造函数
            r'\w+\.\w+\s*\([^)]*\)\s*;?',       # 对象方法调用
            r'var\s+\w+\s*=\s*[^;]+;',          # var 变量声明
            r'let\s+\w+\s*=\s*[^;]+;',          # let 变量声明
            r'const\s+\w+\s*=\s*[^;]+;',        # const 变量声明
        ]
        
        for pattern in js_call_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # 🚫 移除内联事件处理器
        event_patterns = [
            r'\s*on\w+\s*=\s*["\'][^"\']*["\']',  # onclick="..." onload="..."
            r'\s*on\w+\s*=\s*[^>\s]+',           # onclick=handler
        ]
        
        for pattern in event_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # 🚫 移除包含特定关键词的完整行 - 增强版
        deep_filter_patterns = [
            r'.*autoplay.*',
            r'.*pausePosterEnabled.*',
            r'.*poster.*:.*src.*',
            r'.*"width"\s*:\s*\d+.*',
            r'.*"height"\s*:\s*\d+.*',
            r'.*width\s*:\s*\d+.*',  # 不带引号的width
            r'.*height\s*:\s*\d+.*', # 不带引号的height  
            r'.*\.jpg.*width.*height.*',
            r'.*\.mp4.*',
            r'.*player.*config.*',
            r'.*video.*setup.*',
            r'.*videoPlayer\..*',     # videoPlayer对象调用
            r'.*VideoPlayer\(.*',     # VideoPlayer构造函数
            r'.*player\.setup.*',     # player.setup调用
            r'.*\.setup\(.*',         # 任何.setup调用
            r'.*jwplayer.*',
            r'.*videojs.*',
            r'.*\.play\(\).*',
            r'.*\.pause\(\).*',
            r'.*addEventListener.*',
            r'.*document\.getElementById.*',
            r'.*document\.querySelector.*',
            r'.*window\..*',
            r'.*console\..*',
            r'.*function\s*\(\s*\)\s*\{.*',  # 空函数定义
            r'.*function\s+\w+\s*\(\s*\)\s*\{?.*',  # 带名称的函数定义  
            r'.*function\s+\w+.*',    # 任何函数定义
            r'.*}\s*\)\s*;?\s*$',     # 孤立的结束括号和分号
            r'.*^\s*}\s*;?\s*$',      # 只有结束括号的行
            r'.*^\s*\{\s*$',          # 只有开始括号的行
            r'.*^\s*\)\s*;?\s*$',     # 只有结束圆括号的行
        ]
        
        lines = text.split('\n')
        filtered_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否包含深度过滤关键词
            should_filter = False
            for pattern in deep_filter_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    should_filter = True
                    print(f"🚫 过滤JavaScript行: {line[:50]}...")
                    break
            
            if not should_filter:
                filtered_lines.append(line)
        
        # 重新组合文本
        cleaned_text = '\n'.join(filtered_lines)
        
        # 🚫 移除剩余的孤立配置片段
        orphan_patterns = [
            r'\s*}\s*\)\s*;?\s*',  # 孤立的 });
            r'\s*\{\s*\n\s*\}\s*', # 空的对象 {}
            r'\s*\[\s*\n\s*\]\s*', # 空的数组 []
            r'\s*,\s*\n\s*,\s*',   # 多余的逗号
        ]
        
        for pattern in orphan_patterns:
            cleaned_text = re.sub(pattern, '\n', cleaned_text, flags=re.MULTILINE)
        
        # 清理多余的空行
        cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text)
        
        return cleaned_text.strip()
    
    def _get_image_dimensions(self, image_path):
        """
        获取图片尺寸
        :param image_path: 图片文件路径
        :return: (width, height) 或 (None, None)
        """
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                return img.size
        except Exception:
            # 如果PIL不可用或图片无法读取，返回None
            return None, None
    
    def _process_image(self, img_url, base_url, max_size_mb=5):
        """
        处理图片：优先上传到MinIO，失败则使用原始URL
        :param img_url: 图片URL
        :param base_url: 页面基础URL，用于处理相对路径
        :param max_size_mb: 最大图片大小限制(MB)
        :return: (final_url, content_type, error_message, width, height, upload_info)
        """
        try:
            # 处理相对URL
            if img_url.startswith('//'):
                img_url = 'https:' + img_url
            elif img_url.startswith('/'):
                parsed_base = urlparse(base_url)
                img_url = f"{parsed_base.scheme}://{parsed_base.netloc}{img_url}"
            elif not img_url.startswith(('http://', 'https://')):
                img_url = urljoin(base_url, img_url)
            
            print(f"🖼️ 正在处理图片: {img_url}")
            
            # 如果启用MinIO上传，尝试上传
            if self.upload_to_minio and self.storage_manager.is_minio_enabled():
                try:
                    # 先下载到临时位置用于上传
                    session = self._get_session()
                    response = session.get(img_url, timeout=self.timeout, stream=True)
                    response.raise_for_status()
                    
                    # 检查内容类型
                    content_type = response.headers.get('content-type', '')
                    if not content_type.startswith('image/'):
                        print(f"⚠️ 不是有效的图片类型: {content_type}，使用原始URL")
                        return img_url, content_type, None, None, None, {'mode': 'original', 'reason': 'invalid_type'}
                    
                    # 检查文件大小
                    content_length = response.headers.get('content-length')
                    if content_length:
                        size_mb = int(content_length) / (1024 * 1024)
                        if size_mb > max_size_mb:
                            print(f"⚠️ 图片过大: {size_mb:.1f}MB > {max_size_mb}MB，使用原始URL")
                            return img_url, content_type, None, None, None, {'mode': 'original', 'reason': 'too_large'}
                    
                    # 获取文件扩展名
                    file_extension = '.jpg'  # 默认扩展名
                    if 'png' in content_type:
                        file_extension = '.png'
                    elif 'gif' in content_type:
                        file_extension = '.gif'
                    elif 'webp' in content_type:
                        file_extension = '.webp'
                    elif 'jpeg' in content_type or 'jpg' in content_type:
                        file_extension = '.jpg'
                    
                    # 生成临时文件名
                    md5_hash = hashlib.md5(img_url.encode('utf-8')).hexdigest()
                    temp_filename = f"temp_{md5_hash}{file_extension}"
                    temp_file_path = os.path.join("/tmp", temp_filename)
                    if os.path.exists("/tmp") == False:
                        os.makedirs('/tmp')

                    # 下载到临时文件
                    downloaded_size = 0
                    max_size_bytes = max_size_mb * 1024 * 1024
                    
                    with open(temp_file_path, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                downloaded_size += len(chunk)
                                if downloaded_size > max_size_bytes:
                                    f.close()
                                    if os.path.exists(temp_file_path):
                                        os.remove(temp_file_path)
                                    print(f"⚠️ 图片过大: > {max_size_mb}MB，使用原始URL")
                                    return img_url, content_type, None, None, None, {'mode': 'original', 'reason': 'too_large'}
                                f.write(chunk)
                    
                    print(f"📥 临时下载完成: {downloaded_size} bytes")
                    
                    # 获取图片尺寸
                    width, height = self._get_image_dimensions(temp_file_path)
                    
                    # 尝试上传到MinIO
                    success, result_url, error_msg = self.storage_manager.process_image_upload(
                        temp_file_path, img_url, upload_to_minio=True
                    )
                    
                    # 删除临时文件
                    if os.path.exists(temp_file_path):
                        os.remove(temp_file_path)
                    
                    if success and result_url:
                        print(f"🌐 MinIO上传成功: {result_url}")
                        return result_url, content_type, None, width, height, {'mode': 'minio', 'success': True}
                    else:
                        print(f"⚠️ MinIO上传失败: {error_msg}，使用原始URL")
                        return img_url, content_type, None, width, height, {'mode': 'original', 'reason': 'minio_failed', 'error': error_msg}
                        
                except Exception as e:
                    print(f"⚠️ MinIO处理异常: {str(e)}，使用原始URL")
                    return img_url, None, None, None, None, {'mode': 'original', 'reason': 'minio_error', 'error': str(e)}
            else:
                print(f"🔗 MinIO未启用，使用原始URL")
                return img_url, None, None, None, None, {'mode': 'original', 'reason': 'minio_disabled'}
            
        except requests.exceptions.Timeout:
            print(f"⚠️ 图片访问超时，使用原始URL: {img_url}")
            return img_url, None, "图片访问超时", None, None, {'mode': 'original', 'reason': 'timeout'}
        except requests.exceptions.RequestException as e:
            print(f"⚠️ 图片访问失败，使用原始URL: {str(e)}")
            return img_url, None, f"图片访问失败: {str(e)}", None, None, {'mode': 'original', 'reason': 'request_failed'}
        except Exception as e:
            print(f"⚠️ 图片处理异常，使用原始URL: {str(e)}")
            return img_url, None, f"图片处理异常: {str(e)}", None, None, {'mode': 'original', 'reason': 'processing_error'}
    
    def _process_images_in_element(self, element, base_url):
        """
        处理元素中的图片：优先上传到MinIO，失败则使用原始URL
        :param element: lxml元素
        :param base_url: 页面基础URL
        :return: 包含图片信息的字典列表
        """
        images_info = []
        
        # 查找所有img标签
        img_elements = element.xpath('.//img')
        
        for img_element in img_elements:
            try:
                # 获取图片URL
                img_src = img_element.get('src') or img_element.get('data-src') or img_element.get('data-original')
                if not img_src:
                    continue
                
                # 获取图片属性
                img_alt = img_element.get('alt', '图片')
                img_title = img_element.get('title', '')
                
                # 处理图片
                final_url, content_type, error, width, height, upload_info = self._process_image(img_src, base_url)
                
                # 记录图片信息（无论成功还是失败都记录，使用原始URL或MinIO URL）
                img_info = {
                    'original_src': img_src,
                    'final_url': final_url,  # 最终使用的URL（MinIO URL或原始URL）
                    'alt': img_alt,
                    'title': img_title,
                    'element': img_element,
                    'content_type': content_type,
                    'width': width,
                    'height': height,
                    'upload_info': upload_info  # 处理信息
                }
                images_info.append(img_info)
                
                mode_desc = upload_info.get('mode', 'unknown')
                if mode_desc == 'minio':
                    print(f"✅ 图片处理成功 [MinIO]: {img_alt or img_src}")
                elif mode_desc == 'original':
                    reason = upload_info.get('reason', 'unknown')
                    print(f"🔗 使用原始URL [{reason}]: {img_alt or img_src}")
                else:
                    print(f"✅ 图片处理完成 [{mode_desc}]: {img_alt or img_src}")
                    
            except Exception as e:
                print(f"❌ 图片处理异常: {str(e)}")
                # 即使异常也添加原始URL
                img_info = {
                    'original_src': img_src,
                    'final_url': img_src,  # 使用原始URL
                    'alt': img_element.get('alt', '图片'),
                    'title': img_element.get('title', ''),
                    'element': img_element,
                    'content_type': None,
                    'width': None,
                    'height': None,
                    'upload_info': {'mode': 'original', 'reason': 'exception', 'error': str(e)}
                }
                images_info.append(img_info)
                continue
        
        return images_info
    
    def _format_html_content(self, title, xml_content, raw_html=None):
        """
        格式化XML内容为HTML
        :param title: 文章标题
        :param xml_content: trafilatura提取的XML内容
        :param raw_html: 原始HTML（备用）
        :return: 格式化后的HTML
        """
        if not xml_content:
            return raw_html or ""
        
        try:
            soup = BeautifulSoup(xml_content, 'xml')
            main_content = soup.find('main') or soup.find('doc') or soup
            
            # 提取段落文本
            paragraphs = []
            for p in main_content.find_all('p'):
                text = p.get_text().strip()
                if text:
                    text = self._clean_text_content(text)
                    if text:
                        paragraphs.append(text)
            
            # 构建HTML文档
            html_parts = []
            html_parts.append('<html><head></head><body style="line-height: 1.2">')
            
            # 添加标题
            if title:
                html_parts.append(f'<center><h2 style="font-family: 宋体;">{title}</h2></center>')
            
            # 添加段落
            for paragraph in paragraphs:
                formatted_p = f'<p><span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">{paragraph}</span></p>'
                html_parts.append(formatted_p)
            
            html_parts.append('</body></html>')
            
            return '\n'.join(html_parts)
            
        except Exception as e:
            return raw_html or ""
    
    def _extract_content_by_xpath(self, raw_html, title, content_xpath, base_url=None):
        """
        基于XPath提取内容并格式化（支持图片下载）
        :param raw_html: 原始HTML内容
        :param title: 文章标题
        :param content_xpath: 内容XPath表达式
        :param base_url: 页面基础URL，用于处理相对路径图片
        :return: (formatted_html, clean_text)
        """
        try:
            from lxml import html
            
            # 解析HTML
            tree = html.fromstring(raw_html)
            
            # 使用XPath提取内容
            content_elements = tree.xpath(content_xpath)
            if not content_elements:
                return None, None
            
            # 获取第一个匹配的元素
            content_element = content_elements[0]
            
            # 🖼️ 处理图片下载
            images_info = []
            if base_url:
                print("🖼️ 开始处理文章中的图片...")
                images_info = self._process_images_in_element(content_element, base_url)
                print(f"🖼️ 找到 {len(images_info)} 张图片")
            else:
                print("⚠️ 未提供base_url，跳过图片下载")
            
            # 🔄 提取内容段落和图片信息（新的混合提取方式）
            content_items = []  # 存储段落和图片的有序列表
            
            # 创建图片URL到信息的映射
            img_url_map = {}
            for img_info in images_info:
                img_url_map[img_info['original_src']] = img_info
            
            def extract_mixed_content(element):
                """递归提取文本和图片的混合内容"""
                items = []
                
                # 遍历元素的所有子节点
                for child in element:
                    # 🚫 跳过视频、脚本和其他媒体标签（包括嵌套的）
                    if child.tag in ['video', 'iframe', 'embed', 'object', 'audio', 'source', 'track', 'script', 'noscript', 'style']:
                        print(f"🚫 跳过媒体/脚本标签: {child.tag}")
                        continue

                    # 🚫 跳过包含嵌套媒体标签的元素
                    nested_media = child.xpath('.//video | .//iframe | .//embed | .//object | .//audio | .//source | .//track | .//script | .//noscript | .//style')
                    if nested_media:
                        media_tag_names = [elem.tag for elem in nested_media[:3]]  # 只显示前3个，避免日志过长
                        print(f"🚫 跳过包含嵌套媒体标签的元素 {child.tag}: 发现 {media_tag_names}")
                        continue

                    # 🚫 跳过指定class类名的标签
                    skip_classes = ['paper_num', 'section-common-share-wrap', 'ad', 'sidebar', 'footer', 'header', 'nav', 'menu', 'tjewm', 'share']
                    element_class = child.get('class', '')
                    if element_class:
                        for skip_class in skip_classes:
                            if skip_class in element_class.lower():
                                print(f"🚫 跳过指定class的元素 {child.tag}: class='{element_class}'")
                                should_skip_class = True
                                break
                        if 'should_skip_class' in locals():
                            del should_skip_class
                            continue
                    
                    # 🚫 跳过包含JavaScript配置的元素
                    element_text = child.text_content() if hasattr(child, 'text_content') else ''
                    if element_text:
                        js_indicators = ['autoplay', 'pausePosterEnabled', 'poster', '.mp4', 'jwplayer', 'videojs', 'player.setup']
                        for indicator in js_indicators:
                            if indicator in element_text:
                                print(f"🚫 跳过包含JS配置的元素: {indicator}")
                                should_skip_element = True
                                break
                        if 'should_skip_element' in locals():
                            del should_skip_element
                            continue
                    if child.tag == 'img':
                        # 处理图片元素
                        img_src = child.get('src') or child.get('data-src') or child.get('data-original')
                        if img_src in img_url_map:
                            items.append({
                                'type': 'image',
                                'data': img_url_map[img_src]
                            })
                    elif child.tag in ['p', 'div', 'section', 'article', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                        # 处理段落元素
                        # 先检查段落内是否有图片
                        para_imgs = child.xpath('.//img')
                        if para_imgs:
                            # 段落包含图片，需要分别提取文本和图片
                            para_text = child.text_content().strip()
                            if para_text:
                                items.append({
                                    'type': 'text',
                                    'data': para_text
                                })
                            
                            # 添加段落中的图片
                            for img in para_imgs:
                                img_src = img.get('src') or img.get('data-src') or img.get('data-original')
                                if img_src in img_url_map:
                                    items.append({
                                        'type': 'image',
                                        'data': img_url_map[img_src]
                                    })
                        else:
                            # 段落只包含文本
                            para_text = child.text_content().strip()
                            if para_text:
                                items.append({
                                    'type': 'text',
                                    'data': para_text
                                })
                    else:
                        # 递归处理其他元素
                        sub_items = extract_mixed_content(child)
                        items.extend(sub_items)
                
                return items
            
            # 提取混合内容
            if content_element.tag in ['p', 'div', 'section', 'article']:
                content_items = extract_mixed_content(content_element)
                
                # 如果没有提取到结构化内容，回退到原始方法
                if not content_items:
                    full_text = content_element.text_content().strip()
                    if full_text:
                        raw_paragraphs = re.split(r'\n\s*\n', full_text)
                        for para in raw_paragraphs:
                            cleaned_para = re.sub(r'\s+', ' ', para.strip())
                            if cleaned_para:
                                content_items.append({
                                    'type': 'text',
                                    'data': cleaned_para
                                })
            else:
                # 对于其他元素，直接获取文本内容
                full_text = content_element.text_content().strip()
                if full_text:
                    content_items.append({
                        'type': 'text',
                        'data': full_text
                    })
            
            # 过滤和清理内容项
            cleaned_items = []
            for item in content_items:
                if item['type'] == 'text':
                    # 清理文本内容 - 先基础清理，再深度清理JavaScript
                    cleaned_text = self._clean_text_content(item['data'])
                    if cleaned_text:
                        # 🚫 深度清理JavaScript代码和配置
                        cleaned_text = self._deep_clean_javascript(cleaned_text)
                        if cleaned_text:
                            cleaned_text = re.sub(r'\n+', '\n', cleaned_text)
                            cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
                            if cleaned_text:
                                cleaned_items.append({
                                    'type': 'text',
                                    'data': cleaned_text
                                })
                elif item['type'] == 'image':
                    # 图片直接添加
                    cleaned_items.append(item)
            
            if not cleaned_items:
                return None, None
            
            # 🎨 生成格式化HTML（支持本地图片）
            html_parts = []
            html_parts.append('<html><head></head><body style="line-height: 1.2">')
            
            # 添加标题
            # if title:
            #     html_parts.append(f'<center><h2 style="font-family: 宋体;">{title}</h2></center>')
            
            # 添加内容项（文本段落和图片）
            text_parts = []  # 用于生成纯文本
            
            for item in cleaned_items:
                if item['type'] == 'text':
                    # 添加文本段落 - 确保HTML中的文本也被清理
                    paragraph = item['data']
                    # 对HTML中的文本段落也应用清理，去除纠错等内容
                    if '纠错' in paragraph or '【' in paragraph or '[' in paragraph:
                        # 如果段落包含可能的纠错内容，应用额外清理
                        cleaned_paragraph = self._clean_text_content(paragraph)
                        if not cleaned_paragraph.strip():
                            # 如果清理后没有内容，跳过这个段落
                            continue
                        paragraph = cleaned_paragraph
                    
                    formatted_p = f'<p><span style="font-size: 11pt; font-family: 宋体; line-height: 1.5">{paragraph}</span></p>'
                    html_parts.append(formatted_p)
                    text_parts.append(paragraph)
                    
                elif item['type'] == 'image':
                    # 添加图片，统一设置最大宽度为640px
                    img_data = item['data']
                    
                    # 🎯 图片尺寸控制：统一设置最大宽度为640px，保持比例
                    img_style = "max-width: 640px; width: 100%; height: auto;"
                    
                    # 如果有具体尺寸信息，提供更精确的控制
                    if img_data.get('width') and img_data.get('height'):
                        if img_data['width'] > 640:
                            # 计算等比缩放后的高度
                            scale_ratio = 640 / img_data['width']
                            new_height = int(img_data['height'] * scale_ratio)
                            img_style = f"width: 640px; height: {new_height}px;"
                            print(f"🔧 图片尺寸调整: {img_data['width']}x{img_data['height']} -> 640x{new_height}")
                        else:
                            # 小于640px的图片保持原始尺寸
                            img_style = f"width: {img_data['width']}px; height: {img_data['height']}px;"
                            print(f"✅ 图片尺寸保持: {img_data['width']}x{img_data['height']}")
                    else:
                        # 没有尺寸信息的图片（如原始URL），使用CSS限制最大宽度
                        print(f"📐 图片应用最大宽度限制: max-width: 640px")
                    
                    # 🌐 使用最终URL（MinIO URL或原始URL）
                    final_img_url = img_data.get('final_url', '')
                    img_html = f'<p style="text-align: center; margin: 15px 0;"><img src="{final_img_url}" alt="{img_data["alt"]}" style="{img_style}" /></p>'
                    html_parts.append(img_html)
                    
                    # 在文本中添加图片占位符
                    # img_placeholder = f"[图片: {img_data['alt']} - {img_data['final_url']}]"
                    # text_parts.append(img_placeholder)
            
            html_parts.append('</body></html>')
            formatted_html = '\n'.join(html_parts)
            
            # 生成纯文本内容
            clean_text = '\n'.join(text_parts)
            
            return formatted_html, clean_text
            
        except Exception as e:
            print(f"XPath提取失败: {e}")
            return None, None
    
    def extract(self, url, title, content_xpath=None, max_retries=3):
        """
        提取文章内容
        :param url: 文章URL
        :param title: 文章标题
        :param content_xpath: 可选的内容XPath表达式
        :param max_retries: 最大重试次数
        :return: {'text': '文本内容', 'html': 'HTML内容', 'title': '标题', 'error': '错误信息', ...}
        """
        result = {
            'text': '',
            'html': '',
            'title': title,
            'author': '',
            'error': None
        }
        
        # 获取HTML内容（带重试）
        raw_html = None
        for attempt in range(max_retries):
            html_content, error_msg = self._fetch_html(url)
            if html_content:
                raw_html = html_content
                break
            elif attempt == max_retries - 1:
                result['error'] = error_msg or "获取HTML失败"
                return result
            else:
                time.sleep((attempt + 1) * 2)  # 递增等待时间
        
        if not raw_html:
            result['error'] = "无法获取网页内容"
            return result
        
        try:
            # 如果提供了XPath，优先使用XPath方法
            if content_xpath:
                xpath_html, xpath_text = self._extract_content_by_xpath(raw_html, title, content_xpath, url)
                if xpath_html and xpath_text:
                    result['text'] = xpath_text
                    result['html'] = xpath_html
                    result['extraction_method'] = 'xpath'
                    
                    # 同时生成传统方法的结果用于对比
                    traditional_result = self._extract_traditional_method(raw_html, title)
                    result['traditional_text'] = traditional_result.get('text', '')
                    result['traditional_html'] = traditional_result.get('html', '')
                    result['author'] = traditional_result.get('author', '')
                    
                    return result
            
            # 使用传统方法提取内容
            traditional_result = self._extract_traditional_method(raw_html, title)
            result.update(traditional_result)
            result['extraction_method'] = 'traditional'

        except Exception as e:
            result['error'] = f"解析异常: {str(e)}"
        
        return result
    
    def _extract_traditional_method(self, raw_html, title):
        """
        传统方法提取内容（newspaper + trafilatura + GNE）
        :param raw_html: 原始HTML内容
        :param title: 文章标题
        :return: 提取结果字典
        """
        result = {
            'text': '',
            'html': '',
            'author': ''
        }
        
        text_content = ''
        html_content = ''
        
        # 方法1: newspaper库解析
        try:
            article = Article('', language='zh')
            article.set_html(raw_html)
            article.parse()
            text_content = article.text or ''
        except:
            pass
        
        # 方法2: trafilatura解析
        try:
            trafilatura_xml = trafilatura.extract(
                raw_html, 
                output_format="xml", 
                with_metadata=True, 
                include_comments=False, 
                include_tables=True
            )
            if trafilatura_xml:
                html_content = self._format_html_content(title, trafilatura_xml, raw_html)
        except:
            pass
        
        # 方法3: GeneralNewsExtractor补充
        try:
            extractor = GeneralNewsExtractor()
            gne_result = extractor.extract(raw_html)
            if not text_content:
                text_content = gne_result.get('content', '')
            result['author'] = gne_result.get('author', '')
        except:
            pass
        
        # 如果没有格式化HTML，使用原始HTML
        if not html_content:
            html_content = raw_html
        
        # 清理文本内容
        if text_content:
            text_content = self._clean_text_content(text_content)
        
        result['text'] = text_content
        result['html'] = html_content
        
        return result

# 便捷函数
def extract_article(url, title, content_xpath=None, timeout=(10, 30), enable_cache=True, max_retries=3, upload_to_minio=True, config_path="config/config.yml"):
    """
    便捷的文章提取函数
    :param url: 文章URL
    :param title: 文章标题
    :param content_xpath: 可选的内容XPath表达式
    :param timeout: 超时设置
    :param enable_cache: 是否启用缓存
    :param max_retries: 最大重试次数
    :param upload_to_minio: 是否上传图片到MinIO（默认开启）
    :param config_path: 配置文件路径
    :return: {'text': '文本内容', 'html': 'HTML内容', 'title': '标题', 'error': '错误信息'}
    """
    extractor = ArticleExtractor(
        timeout=timeout, 
        enable_cache=enable_cache, 
        upload_to_minio=upload_to_minio,
        config_path=config_path
    )
    return extractor.extract(url, title, content_xpath, max_retries=max_retries)

if __name__ == "__main__":
    # 测试示例
    test_url = "http://finance.people.com.cn/n1/2025/0918/c1004-40566682.html"  # 示例URL
    test_title = '测试'
    content_xpath = '//div[@class="rm_txt_con cf"]'
    
    print("🔄 开始提取文章内容...")

    # 测试1: 使用XPath方法（指定正确的配置文件路径）
    print("\n📋 测试1: 使用XPath方法提取")
    result_xpath = extract_article(test_url, test_title, content_xpath, config_path="../config/config.yml")
    
    if result_xpath['error']:
        print(f"❌ XPath提取失败: {result_xpath['error']}")
    else:
        print(f"✅ XPath提取成功")
        print(f"📰 标题: {result_xpath['title']}")
        print(f"📰 作者: {result_xpath['author']}")
        print(f"📝 XPath文本长度: {len(result_xpath['text'])} 字符")
        print(f"🌐 XPath HTML长度: {len(result_xpath['html'])} 字符")
        print(f"📄 XPath文本预览: {result_xpath['text'][:200]}...")
        print(f"📄 XPath HTML预览: {result_xpath['html']}...")
        
        if 'traditional_text' in result_xpath:
            print(f"\n📊 对比信息:")
            print(f"📝 传统方法文本长度: {len(result_xpath['traditional_text'])} 字符")
            print(f"🌐 传统方法HTML长度: {len(result_xpath['traditional_html'])} 字符")
            print(f"📄 传统方法文本预览: {result_xpath['traditional_html']}")
    
    # 测试2: 使用传统方法（传统方法不处理图片）
    print("\n📋 测试2: 使用传统方法提取")
    result_traditional = extract_article(test_url, test_title, config_path="../config/config.yml")  # 不传递content_xpath
    if result_traditional['error']:
        print(f"❌ 传统方法提取失败: {result_traditional['error']}")
    else:
        print(f"📰 标题: {result_traditional['title']}")
        print(f"📰 作者: {result_traditional['author']}")
        print(f"📝 传统文本长度: {len(result_traditional['text'])} 字符")
        print(f"🌐 传统HTML长度: {len(result_traditional['html'])} 字符")
        print(f"📄 传统文本预览: {result_traditional['text']}...")
