import json
import traceback
import re
import requests

class Format:
    @staticmethod
    def post_html_format(content_html, url, title):
        download = None
        body = {
            'htmlStr': content_html,
            'htmlAddr': url,
            'title': title,
            'subtitle': '',
            'isSaveFile': True
        }
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
        }
        try:
            # response = requests.post('https://bot-collection-data.botsmart.cn:19628/api/format/htmlFormat', headers=headers, json=body, timeout=60)
            response = requests.post('http://127.0.0.1:8200/format/htmlFormat', headers=headers, json=body, timeout=160)
        except Exception as e:
            traceback.print_exc()
            # Format.ding(e)
            return None, download
        # print(response)
        print(response.text)
        res = json.loads(response.text)
        if 'code' in res:
            if res['code'] == 200:
                content = re.sub(r"</html>\n.*", "</html>", res['data']['html_content'])
                if 'download' in res['data']:
                    download = res['data']['download']
                else:
                    download = 'success'
            else:
                Format.ding(response.text)
                content = None
            return content, download
        elif 'status' in res:
            if res['status'] == 500:
                return None, download
    @staticmethod
    def ding(content):
        message = {
            "msgtype": "text",
            "text": {
                "content": '全国各地政府: content_html转换错误\n' + str(content)
            }
        }
        message_json = json.dumps(message)
        send_message = requests.post(
            url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2deca73e-eb06-4f1b-88d4-1b1881cadbd3',
            data=message_json, headers={"Content-Type": "application/json"})
        print(send_message.text)