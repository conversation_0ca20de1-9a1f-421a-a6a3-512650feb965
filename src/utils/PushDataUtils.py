import json
import time
import traceback
import re as re
import requests

from src.sqlite.sql import Sql
from src.upload.uploads import Uploads


class Push:
    requestPath = '/api/collection/storage/cbdn-zcfg'
    pid = 'SP-1703493752759'
    taskKey = 'DA933C6CAEF6DE8481452290069ABF9D'

    # 传输多条数据
    @classmethod
    def send_ad_datas(cls, datas):
        for d in datas:
            print(d['url'])
            print(datas.index(d))
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            try:
                cls.send_add_data(d)
            except Exception as e:
                traceback.print_exc()
                time.sleep(2)

    # 传输多条数据
    @classmethod
    def send_ad_datas_qx(cls, datas):
        for d in datas:
            print(d['url'])
            print(datas.index(d))
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            try:
                cls.send_add_data_qx(d)
            except Exception as e:
                traceback.print_exc()
                time.sleep(2)

    # 传输多条数据_人事变动
    @classmethod
    def send_ad_datas_rsbd(cls, datas):
        for d in datas:
            print(d['url'])
            print(datas.index(d))
            print(time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
            try:
                cls.send_add_data_rsbd(d)
            except Exception as e:
                traceback.print_exc()
                time.sleep(2)

    @classmethod
    def send_add_data(cls, data):
        re = cls.send_ad_data(data)
        push_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        if re:
            if 'msg' in re:
                if re['msg'] == '成功':
                    Sql.update_data_repository(uuid=data['uuid'], receipt=json.dumps(re), lab=1, push_time=push_time)
                elif re['msg'] == '跳过':
                    Sql.update_data_repository(uuid=data['uuid'], receipt=json.dumps(re), lab=5, push_time=push_time)

    @classmethod
    def send_add_data_qx(cls, data):
        re = cls.send_ad_data_qx(data)
        push_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        if re:
            if 'msg' in re:
                if re['msg'] == '成功':
                    Sql.update_data_repository_qx(uuid=data['uuid'], receipt=json.dumps(re), lab=1, push_time=push_time)
                elif re['msg'] == '跳过':
                    Sql.update_data_repository_qx(uuid=data['uuid'], receipt=json.dumps(re), lab=5, push_time=push_time)

    @classmethod
    def send_add_data_rsbd(cls, data):
        re = cls.send_ad_data_rsbd(data)
        push_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        if re:
            if 'msg' in re:
                if re['msg'] == '成功':
                    Sql.update_data_repository(uuid=data['uuid'], receipt=json.dumps(re), lab=1, push_time=push_time)
                    # Sql.update_data_content_html_su(uuid=data['uuid'], content_html=data['formatContent'], receipt=json.dumps(re), lab=1)
                elif re['msg'] == '跳过':
                    Sql.update_data_repository(uuid=data['uuid'], receipt=json.dumps(re), lab=5, push_time=push_time)

    # 长沙_妙笔  传输单条数据
    @classmethod
    def send_ad_data(cls, data):
        body = {
            'dataList':[
                {
                    'uuidc': data['uuid'],
                    'title': data['title'],
                    'formatContent': data['formatContent'],
                    'sourceName': data['sourceName'],
                    'library': data['library'],
                    'label': data['label'],
                    'url': data['url'],
                    'releaseTime': data['releaseTime']
                }
            ]
        }
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
        }
        try:
            response = requests.post('https://writing.botsmart.cn/api/import/learning-garden', headers=headers, json=body, timeout=60)
        except Exception as e:
            traceback.print_exc()
            return None
        # print(response)
        print(response.text)
        re = json.loads(response.text)
        return re

    # 长沙_妙笔  传输单条数据
    @classmethod
    def send_ad_data_qx(cls, data):
        body = {
            'dataList':[
                {
                    'uuidc': data['uuid'],
                    'title': data['title'],
                    'formatContent': data['formatContent'],
                    'sourceName': data['sourceName'],
                    'library': data['library'],
                    'label': data['label'],
                    'url': data['url'],
                    'location': data['location'],
                    'releaseTime': data['releaseTime']
                }
            ]
        }
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
        }
        try:
            response = requests.post('https://writing.botsmart.cn/api/import/local_policies', headers=headers, json=body, timeout=60)
        except Exception as e:
            traceback.print_exc()
            return None
        # print(response)
        print(response.text)
        re = json.loads(response.text)
        return re

    # 长沙_妙笔  传输单条数据_人事变动
    @classmethod
    def send_ad_data_rsbd(cls, data):
        body = {
            'dataList':[
                {
                    'uuidc': data['uuid'],
                    'title': data['title'],
                    'formatContent': data['formatContent'],
                    'sourceName': data['sourceName'],
                    'library': data['library'],
                    'label': data['label'],
                    'url': data['url'],
                    'releaseTime': data['releaseTime']
                }
            ]
        }
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
        }
        try:
            response = requests.post('https://writing.botsmart.cn/api/import/appoint-resign', headers=headers, json=body, timeout=60)
        except Exception as e:
            traceback.print_exc()
            return None
        # print(response)
        print(response.text)
        re = json.loads(response.text)
        return re

    # 通过html接口转换文本
    @classmethod
    def post_html_format(cls, content_html, url, title):
        body = {
            'htmlStr': content_html,
            'htmlAddr': url,
            'title': title,
            'subtitle': '',
            'isSaveFile': True
        }
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/72.0.3626.121 Safari/537.36',
        }
        try:
            response = requests.post('https://bot-collection-data.botsmart.cn:19628/api/format/htmlFormat', headers=headers, json=body, timeout=60)
            # response = requests.post('http://127.0.0.1:8200/format/htmlFormat', headers=headers, json=body, timeout=60)
        except Exception as e:
            traceback.print_exc()
            cls.ding(e)
            return None
        # print(response)
        print(response.text)
        res = json.loads(response.text)
        if 'code' in res:
            if res['code'] == 200:
                content = re.sub(r"</html>\n.*", "</html>", re['data']['html_content'])
            else:
                cls.ding(response.text)
                content = None
            return content
        elif 'status' in res:
            if res['status'] == 500:
                return None
    @classmethod
    def ding(cls, content):
        message = {
            "msgtype": "text",
            "text": {
                "content": str(content)
            }
        }
        message_json = json.dumps(message)
        send_message = requests.post(
            url='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=2deca73e-eb06-4f1b-88d4-1b1881cadbd3',
            data=message_json, headers={"Content-Type": "application/json"})
        print(send_message.text)



if __name__ == '__main__':
    Push.ding('1')