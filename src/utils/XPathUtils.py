import requests
from lxml import html, etree
import logging

class XPathUtils:
    """XPath工具类，用于网页数据抓取和解析"""
    
    @staticmethod
    def get_html_content(url, headers=None, timeout=30):
        """
        获取网页HTML内容
        
        Args:
            url: 网页URL
            headers: 请求头
            timeout: 超时时间
            
        Returns:
            tuple: (html_content, status_code)
        """
        if headers is None:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        
        try:
            response = requests.get(url, headers=headers, timeout=timeout)
            response.encoding = response.apparent_encoding
            return response.text, response.status_code
        except Exception as e:
            logging.error(f"获取网页内容失败: {url}, 错误: {str(e)}")
            return None, None
    
    @staticmethod
    def safe_xpath_extract(tree, xpath, return_all=False):
        """
        安全的XPath提取函数 [[memory:889167]]
        
        Args:
            tree: lxml解析树
            xpath: XPath表达式
            return_all: 是否返回所有匹配结果，默认False只返回第一个
            
        Returns:
            str/list: 提取的文本内容或列表
        """
        try:
            elements = tree.xpath(xpath)
            if not elements:
                logging.warning(f"XPath未找到匹配元素: {xpath}")
                return [] if return_all else ''
            
            # 如果找到多个元素但return_all为False，记录警告
            if len(elements) > 1 and not return_all:
                logging.warning(f"XPath找到{len(elements)}个匹配元素，但只返回第一个: {xpath}")
            
            if return_all:
                # 返回所有匹配结果
                result = []
                for element in elements:
                    if isinstance(element, str):
                        result.append(element.strip())
                    else:
                        result.append(element.text_content().strip() if hasattr(element, 'text_content') else str(element).strip())
                return result
            else:
                # 只返回第一个元素
                element = elements[0]
                if isinstance(element, str):
                    return element.strip()
                else:
                    return element.text_content().strip() if hasattr(element, 'text_content') else str(element).strip()
                    
        except Exception as e:
            logging.error(f"XPath提取失败: {xpath}, 错误: {str(e)}")
            return [] if return_all else ''
    
    @staticmethod
    def safe_xpath_extract_raw(tree, xpath, return_all=False):
        """
        安全的XPath提取函数（保留原始HTML格式）
        
        Args:
            tree: lxml解析树
            xpath: XPath表达式
            return_all: 是否返回所有匹配结果，默认False只返回第一个
            
        Returns:
            str/list: 提取的原始HTML内容或列表
        """
        try:
            elements = tree.xpath(xpath)
            if not elements:
                logging.warning(f"XPath未找到匹配元素: {xpath}")
                return [] if return_all else ''
            
            # 如果找到多个元素但return_all为False，记录警告
            if len(elements) > 1 and not return_all:
                logging.warning(f"XPath找到{len(elements)}个匹配元素，但只返回第一个: {xpath}")
            
            if return_all:
                # 返回所有匹配结果的原始HTML
                result = []
                for element in elements:
                    if isinstance(element, str):
                        result.append(element)
                    else:
                        # 使用etree.tostring获取原始HTML
                        raw_html = etree.tostring(element, encoding='unicode', method='html')
                        result.append(raw_html)
                return result
            else:
                # 只返回第一个元素的原始HTML
                element = elements[0]
                if isinstance(element, str):
                    return element
                else:
                    # 使用etree.tostring获取原始HTML
                    raw_html = etree.tostring(element, encoding='unicode', method='html')
                    return raw_html
                    
        except Exception as e:
            logging.error(f"XPath提取失败: {xpath}, 错误: {str(e)}")
            return [] if return_all else ''
    
    @staticmethod
    def parse_html(html_content):
        """
        解析HTML内容为lxml树对象
        
        Args:
            html_content: HTML内容字符串
            
        Returns:
            tree: lxml解析树对象
        """
        try:
            return html.fromstring(html_content)
        except Exception as e:
            logging.error(f"HTML解析失败: {str(e)}")
            return None
    
    @staticmethod
    def extract_links(tree, xpath):
        """
        提取链接地址
        
        Args:
            tree: lxml解析树
            xpath: XPath表达式
            
        Returns:
            list: 链接列表
        """
        try:
            links = tree.xpath(xpath)
            result = []
            for link in links:
                if hasattr(link, 'get'):
                    href = link.get('href', '')
                    if href:
                        result.append(href)
                elif isinstance(link, str):
                    result.append(link)
            return result
        except Exception as e:
            logging.error(f"链接提取失败: {xpath}, 错误: {str(e)}")
            return [] 