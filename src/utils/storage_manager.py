#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储管理器
功能：MinIO对象存储和MySQL数据库操作
"""

import os
import yaml
import logging
import hashlib
import threading
from datetime import datetime
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlparse

# MinIO相关
from minio import Minio
from minio.error import S3Error

# 数据库相关
from sqlalchemy import create_engine, Column, String, DateTime, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

Base = declarative_base()

class LocalFileUrl(Base):
    """本地文件URL映射表"""
    __tablename__ = 'local_file_url'
    
    sid = Column(String(255), primary_key=True, comment='MD5哈希值')
    lfu_attr_key = Column(String(255), comment='属性类型(src)')
    lfu_url = Column(String(500), comment='MinIO访问URL')
    lfu_old_url = Column(String(500), comment='原始图片URL')
    lfu_create_time = Column(String(50), comment='创建时间')

class StorageManager:
    """存储管理器类"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, config_path: str = "../config/config.yml"):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(StorageManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, config_path: str = "../config/config.yml"):
        """初始化存储管理器"""
        if self._initialized:
            return
            
        self.config_path = config_path
        self.config = self._load_config()
        
        # MinIO客户端
        self.minio_client = None
        self.minio_enabled = False
        
        # 数据库引擎和会话
        self.db_engine = None
        self.db_session_factory = None
        self.db_enabled = False
        
        # 初始化组件
        self._init_minio()
        self._init_database()
        
        self._initialized = True
        logger.info("存储管理器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 支持环境变量覆盖
            self._override_with_env(config)
            return config
            
        except FileNotFoundError:
            logger.warning(f"配置文件 {self.config_path} 不存在，使用默认配置")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _override_with_env(self, config: Dict[str, Any]):
        """使用环境变量覆盖配置"""
        # MinIO配置
        if os.getenv('MINIO_ENDPOINT'):
            config['minio']['endpoint'] = os.getenv('MINIO_ENDPOINT')
        if os.getenv('MINIO_ACCESS_KEY'):
            config['minio']['access_key'] = os.getenv('MINIO_ACCESS_KEY')
        if os.getenv('MINIO_SECRET_KEY'):
            config['minio']['secret_key'] = os.getenv('MINIO_SECRET_KEY')
        
        # 数据库配置
        if os.getenv('DB_HOST'):
            config['database']['host'] = os.getenv('DB_HOST')
        if os.getenv('DB_PORT'):
            config['database']['port'] = int(os.getenv('DB_PORT'))
        if os.getenv('DB_USERNAME'):
            config['database']['username'] = os.getenv('DB_USERNAME')
        if os.getenv('DB_PASSWORD'):
            config['database']['password'] = os.getenv('DB_PASSWORD')
        if os.getenv('DB_DATABASE'):
            config['database']['database'] = os.getenv('DB_DATABASE')
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'minio': {'enabled': False},
            'database': {'enabled': False},
            'image': {'local_dir': 'src/news_images'},
            'logging': {'level': 'INFO'}
        }
    
    def _init_minio(self):
        """初始化MinIO客户端"""
        try:
            minio_config = self.config.get('minio', {})
            if not minio_config.get('enabled', False):
                logger.info("MinIO未启用")
                return
            
            endpoint = minio_config.get('endpoint', '').replace('http://', '').replace('https://', '')
            port = minio_config.get('port', 9000)
            access_key = minio_config.get('access_key')
            secret_key = minio_config.get('secret_key')
            
            if not all([endpoint, access_key, secret_key]):
                logger.error("MinIO配置不完整")
                return
            
            # 创建MinIO客户端
            self.minio_client = Minio(
                f"{endpoint}:{port}",
                access_key=access_key,
                secret_key=secret_key,
                secure=False  # 根据实际情况调整
            )
            
            # 测试连接
            bucket_name = minio_config.get('bucket_name', 'botsmart')
            if self.minio_client.bucket_exists(bucket_name):
                self.minio_enabled = True
                logger.info(f"MinIO连接成功，存储桶: {bucket_name}")
            else:
                logger.error(f"MinIO存储桶 {bucket_name} 不存在")
                
        except Exception as e:
            logger.error(f"MinIO初始化失败: {e}")
            self.minio_client = None
            self.minio_enabled = False
    
    def _init_database(self):
        """初始化数据库连接"""
        try:
            db_config = self.config.get('database', {})
            if not db_config.get('enabled', False):
                logger.info("数据库未启用")
                return
            
            # 构建数据库连接字符串
            # 注意: 密码中如果包含特殊字符，需要URL编码
            from urllib.parse import quote_plus
            password_encoded = quote_plus(str(db_config['password']))
            
            db_url = (f"mysql+pymysql://{db_config['username']}:{password_encoded}"
                     f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
                     f"?charset={db_config.get('charset', 'utf8mb4')}")
            
            # 创建数据库引擎
            self.db_engine = create_engine(
                db_url,
                pool_size=db_config.get('pool_size', 20),
                max_overflow=db_config.get('max_overflow', 10),
                pool_timeout=db_config.get('pool_timeout', 30),
                pool_recycle=db_config.get('pool_recycle', 3600),
                echo=db_config.get('echo', False)
            )
            
            # 创建会话工厂
            self.db_session_factory = sessionmaker(bind=self.db_engine)
            
            # 测试连接
            with self.db_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self.db_enabled = True
            logger.info("数据库连接成功")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            self.db_engine = None
            self.db_session_factory = None
            self.db_enabled = False
    
    def upload_to_minio(self, file_path: str, object_name: str = None) -> Tuple[bool, str]:
        """
        上传文件到MinIO
        :param file_path: 本地文件路径
        :param object_name: MinIO中的对象名称，如果为None则使用文件名
        :return: (是否成功, MinIO访问URL或错误信息)
        """
        if not self.minio_enabled or not self.minio_client:
            return False, "MinIO未启用或连接失败"
        
        try:
            minio_config = self.config.get('minio', {})
            bucket_name = minio_config.get('bucket_name', 'botsmart')
            folder_path = minio_config.get('folder_path', 'temp_col_file/')
            
            if object_name is None:
                object_name = os.path.basename(file_path)
            
            # 确保文件夹路径以/结尾
            if folder_path and not folder_path.endswith('/'):
                folder_path += '/'
            
            full_object_name = f"{folder_path}{object_name}"
            
            # 上传文件
            self.minio_client.fput_object(
                bucket_name,
                full_object_name,
                file_path,
                content_type=self._get_content_type(file_path)
            )
            
            # 生成访问URL
            show_https_url = minio_config.get('show_https_url')
            show_url = minio_config.get('show_url')
            
            if show_https_url:
                access_url = f"{show_https_url}/{bucket_name}/{full_object_name}"
            else:
                access_url = f"{show_url}:{minio_config.get('port', 9000)}/{bucket_name}/{full_object_name}"
            
            logger.info(f"文件上传成功: {access_url}")
            return True, access_url
            
        except S3Error as e:
            error_msg = f"MinIO上传失败: {e}"
            logger.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"上传异常: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _get_content_type(self, file_path: str) -> str:
        """根据文件扩展名获取Content-Type"""
        ext = os.path.splitext(file_path)[1].lower()
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.svg': 'image/svg+xml'
        }
        return content_types.get(ext, 'application/octet-stream')
    
    def save_file_mapping(self, sid: str, minio_url: str, original_url: str, attr_key: str = 'src') -> bool:
        """
        保存文件URL映射到数据库
        :param sid: MD5哈希值
        :param minio_url: MinIO访问URL
        :param original_url: 原始URL
        :param attr_key: 属性键
        :return: 是否成功
        """
        if not self.db_enabled or not self.db_session_factory:
            logger.warning("数据库未启用，跳过URL映射保存")
            return False
        
        session: Session = None
        try:
            session = self.db_session_factory()
            
            # 查询是否已存在
            existing = session.query(LocalFileUrl).filter_by(sid=sid).first()
            
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            if existing:
                # 更新现有记录
                existing.lfu_url = minio_url
                existing.lfu_old_url = original_url
                existing.lfu_attr_key = attr_key
                existing.lfu_create_time = current_time
                logger.info(f"更新文件映射: {sid}")
            else:
                # 创建新记录
                new_mapping = LocalFileUrl(
                    sid=sid,
                    lfu_url=minio_url,
                    lfu_old_url=original_url,
                    lfu_attr_key=attr_key,
                    lfu_create_time=current_time
                )
                session.add(new_mapping)
                logger.info(f"创建文件映射: {sid}")
            
            session.commit()
            return True
            
        except SQLAlchemyError as e:
            if session:
                session.rollback()
            logger.error(f"数据库操作失败: {e}")
            return False
        except Exception as e:
            if session:
                session.rollback()
            logger.error(f"保存文件映射异常: {e}")
            return False
        finally:
            if session:
                session.close()
    
    def get_file_mapping(self, sid: str) -> Optional[Dict[str, str]]:
        """
        根据SID获取文件映射
        :param sid: MD5哈希值
        :return: 文件映射信息或None
        """
        if not self.db_enabled or not self.db_session_factory:
            return None
        
        session: Session = None
        try:
            session = self.db_session_factory()
            mapping = session.query(LocalFileUrl).filter_by(sid=sid).first()
            
            if mapping:
                return {
                    'sid': mapping.sid,
                    'minio_url': mapping.lfu_url,
                    'original_url': mapping.lfu_old_url,
                    'attr_key': mapping.lfu_attr_key,
                    'create_time': mapping.lfu_create_time
                }
            return None
            
        except SQLAlchemyError as e:
            logger.error(f"查询文件映射失败: {e}")
            return None
        finally:
            if session:
                session.close()
    
    def generate_sid(self, url: str) -> str:
        """
        生成URL的MD5哈希值作为SID
        :param url: 原始URL
        :return: MD5哈希值
        """
        return hashlib.md5(url.encode('utf-8')).hexdigest()
    
    def process_image_upload(self, local_image_path: str, original_url: str, 
                           upload_to_minio: bool = True) -> Tuple[bool, str, Optional[str]]:
        """
        处理图片上传流程
        :param local_image_path: 本地图片路径
        :param original_url: 原始图片URL
        :param upload_to_minio: 是否上传到MinIO
        :return: (是否成功, 最终使用的URL, 错误信息)
        """
        try:
            # 生成SID
            sid = self.generate_sid(original_url)
            
            # 检查是否已存在映射
            if upload_to_minio and self.db_enabled:
                existing_mapping = self.get_file_mapping(sid)
                if existing_mapping and existing_mapping.get('minio_url'):
                    logger.info(f"使用已存在的MinIO映射: {sid}")
                    return True, existing_mapping['minio_url'], None
            
            # 如果不上传到MinIO，直接返回本地路径
            if not upload_to_minio:
                logger.info(f"本地模式，使用本地路径: {local_image_path}")
                return True, local_image_path, None
            
            # 上传到MinIO
            # 使用SID + 原始文件扩展名作为MinIO对象名
            original_ext = os.path.splitext(urlparse(original_url).path)[1]
            if not original_ext:
                original_ext = os.path.splitext(local_image_path)[1]
            
            minio_object_name = f"{sid}{original_ext}"
            
            success, result = self.upload_to_minio(local_image_path, minio_object_name)
            
            if success:
                # 保存URL映射
                self.save_file_mapping(sid, result, original_url)
                logger.info(f"图片上传MinIO成功: {result}")
                return True, result, None
            else:
                # 上传失败，降级到本地模式
                logger.warning(f"MinIO上传失败，降级到本地模式: {result}")
                return True, local_image_path, f"MinIO上传失败: {result}"
                
        except Exception as e:
            error_msg = f"图片上传处理异常: {e}"
            logger.error(error_msg)
            return False, local_image_path, error_msg
    
    def is_minio_enabled(self) -> bool:
        """检查MinIO是否启用"""
        return self.minio_enabled
    
    def is_database_enabled(self) -> bool:
        """检查数据库是否启用"""
        return self.db_enabled
    
    def get_config(self) -> Dict[str, Any]:
        """获取配置信息"""
        return self.config.copy()

# 全局存储管理器实例
_storage_manager = None
_storage_lock = threading.Lock()

def get_storage_manager(config_path: str = "../config/config.yml") -> StorageManager:
    """获取存储管理器单例"""
    global _storage_manager
    
    if _storage_manager is None:
        with _storage_lock:
            if _storage_manager is None:
                _storage_manager = StorageManager(config_path)
    
    return _storage_manager 