#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试XPath提取标题的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils.XPathUtils import XPathUtils

def test_xpath_extraction():
    """测试XPath提取"""
    
    # 测试URL
    test_url = "http://paper.ce.cn/pc/content/202509/23/content_320869.html"
    
    # XPath表达式
    xpath = '//h2[contains(@class,"art-title")]/p/text()'
    
    print(f"🔄 测试URL: {test_url}")
    print(f"🔄 XPath: {xpath}")
    print("-" * 60)
    
    # 获取HTML内容
    print("1. 获取HTML内容...")
    html_content, status_code = XPathUtils.get_html_content(test_url)
    
    if not html_content:
        print("❌ 获取HTML内容失败")
        return
    
    print(f"✅ 获取成功，状态码: {status_code}")
    print(f"📄 HTML长度: {len(html_content)} 字符")
    
    # 解析HTML
    print("\n2. 解析HTML...")
    tree = XPathUtils.parse_html(html_content)
    
    if tree is None:
        print("❌ HTML解析失败")
        return
    
    print("✅ HTML解析成功")
    
    # 测试XPath提取
    print("\n3. 测试XPath提取...")
    result = XPathUtils.safe_xpath_extract(tree, xpath)
    
    print(f"📝 提取结果: '{result}'")
    print(f"📏 结果长度: {len(result) if result else 0}")
    
    # 检查HTML中是否包含目标元素
    print("\n4. 检查HTML内容...")
    if 'art-title' in html_content:
        print("✅ HTML中包含 'art-title' 类")
    else:
        print("❌ HTML中不包含 'art-title' 类")
    
    if '雪莲绽放新时代' in html_content:
        print("✅ HTML中包含目标标题文本")
    else:
        print("❌ HTML中不包含目标标题文本")
    
    # 尝试其他XPath表达式
    print("\n5. 尝试其他XPath表达式...")
    
    alternative_xpaths = [
        '//h2[@class="art-title"]/p/text()',
        '//h2[contains(@class,"art-title")]/p',
        '//h2[contains(@class,"art-title")]//text()',
        '//*[contains(@class,"art-title")]//text()',
        '//title/text()',
    ]
    
    for i, alt_xpath in enumerate(alternative_xpaths, 1):
        alt_result = XPathUtils.safe_xpath_extract(tree, alt_xpath)
        print(f"   {i}. {alt_xpath}")
        print(f"      结果: '{alt_result}'")
    
    # 显示HTML片段
    print("\n6. 显示相关HTML片段...")
    if 'art-title' in html_content:
        start_pos = html_content.find('art-title') - 50
        end_pos = html_content.find('art-title') + 200
        if start_pos < 0:
            start_pos = 0
        if end_pos > len(html_content):
            end_pos = len(html_content)
        
        html_snippet = html_content[start_pos:end_pos]
        print("相关HTML片段:")
        print(html_snippet)

if __name__ == "__main__":
    test_xpath_extraction()
